import pytest

from datetime import timedelta

from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLRoomStatusName
from auth.permissions import READ_ANY_ROOM
from tests_component import harness


@pytest.mark.asyncio
async def test_get_room_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            id
            name
        }}
    }}
    """
    )
    assert result == {
        "data": {"room": None},
        "errors": [
            {
                "locations": [{"column": 9, "line": 3}],
                "message": "Not Authorized: No authorization found",
                "path": ["room"],
            }
        ],
    }


@pytest.mark.asyncio
async def test_get_room_no_permissions() -> None:
    result = harness.user_without_permissions_client.execute_graphql(
        f"""
        query {{
          room(id: "{harness.room_0.id}") {{
              id
              name
          }}
        }}
        """
    )
    assert result == {
        "data": {"room": None},
        "errors": [
            {
                "locations": [{"column": 11, "line": 3}],
                "message": f"User does not have permission '{READ_ANY_ROOM}'",
                "path": ["room"],
            }
        ],
    }


def test_get_room_turnover() -> None:
    miss = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}",
                    turnoverId: "not-a-real-id"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert miss == {
        "data": {
            "room": {
                "turnovers": [],
            }
        }
    }

    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}",
                    turnoverId: "turnover-case:{harness.case_0.case_id}-case:{harness.case_1.case_id}"
                }}
            ) {{
                status {{
                    name
                }}
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                        "status": {"name": "CLEANING"},
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room_turnovers() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            id
            name
            organizationId
            organization {{
                id
            }}
            siteId
            site {{
                id
            }}
            cameras {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
            defaultCamera {{
              name
            }}
            primeTimeConfig {{
                roomId
            }}
            privacyEnabled
            isForecastingEnabled
            apellaCases(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
            turnovers(
                query: {{
                    minEndTime: "{(harness.case_0.scheduled_end_time - timedelta(days=1)).isoformat()}",
                    maxStartTime: "{(harness.case_0.scheduled_start_time + timedelta(days=1)).isoformat()}"
                }}
            ) {{
                precedingCase {{
                    id
                }}
                followingCase {{
                    id
                }}
                type
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {
                "id": harness.room_0.id,
                "name": harness.room_0.name,
                "organizationId": harness.room_0.org_id,
                "organization": {"id": harness.room_0.org_id},
                "siteId": harness.room_0.site_id,
                "site": {"id": harness.room_0.site_id},
                "cameras": {
                    "edges": [
                        {
                            "node": {
                                "id": harness.camera_0.id,
                            }
                        },
                        {
                            "node": {
                                "id": harness.camera_1.id,
                            }
                        },
                    ]
                },
                "defaultCamera": {
                    "name": harness.camera_1.name,
                },
                "primeTimeConfig": {"roomId": harness.room_0.id},
                "privacyEnabled": False,
                "isForecastingEnabled": True,
                "apellaCases": {
                    "edges": [
                        {"node": {"id": "case:" + harness.case_0.case_id}},
                        {"node": {"id": "case:" + harness.case_1.case_id}},
                    ]
                },
                "turnovers": [
                    {
                        "precedingCase": {"id": "case:" + harness.case_0.case_id},
                        "followingCase": {"id": "case:" + harness.case_1.case_id},
                        "type": "LIVE",
                    }
                ],
            }
        }
    }


@pytest.mark.asyncio
async def test_get_room_dashboard_events() -> None:
    result = harness.labeler_client.execute_graphql(
        f"""
    query {{
        room(id: "{harness.room_0.id}") {{
            roomEvents(query: {{
                minStartTime: "2021-05-01T00:00:00",
                maxStartTime: "2021-05-02T00:00:00",
                includeDashboardEventsOnly: true
            }}) {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "room": {"roomEvents": {"edges": [{"node": {"id": harness.event_6.id}}]}}
        }
    }


async def test_get_room_as_org_user_requires_same_org() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_3.id}") {{
                name
            }}
        }}
        """
    )
    assert result == {"data": {"room": None}}


@pytest.mark.asyncio
async def test_get_room_status() -> None:
    apella_schema = ApellaSchema()
    query = apella_schema.Query.room.args(id=harness.room_0.id).select(
        apella_schema.Room.id,
        apella_schema.Room.status.select(
            apella_schema.RoomStatus.name,
            apella_schema.RoomStatus.since,
        ),
    )
    results = harness.service_account_client.query_graphql_from_schema(query)

    assert results.room.status.name == GQLRoomStatusName.IDLE


# Tests for date-aware room status field (RT-2172)


@pytest.mark.asyncio
async def test_get_room_status_with_date_parameters() -> None:
    """Test that room status field accepts and uses date parameters."""
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                name
                status(
                    minEndTime: "{query_min_end_time.isoformat()}"
                    maxStartTime: "{query_max_start_time.isoformat()}"
                ) {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    assert result["data"]["room"]["id"] == harness.room_0.id

    room_status = result["data"]["room"]["status"]
    assert "name" in room_status
    assert "since" in room_status
    assert "calculatedAt" in room_status


@pytest.mark.asyncio
async def test_get_room_status_without_date_parameters_backward_compatibility() -> None:
    """Test that room status field works without date parameters (backward compatibility)."""
    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                name
                status {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    assert result["data"]["room"]["id"] == harness.room_0.id

    room_status = result["data"]["room"]["status"]
    assert "name" in room_status
    assert "since" in room_status
    assert "calculatedAt" in room_status


@pytest.mark.asyncio
async def test_get_room_status_with_partial_date_parameters() -> None:
    """Test room status field behavior when only one date parameter is provided."""
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))

    # Test with only minEndTime
    result1 = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                status(minEndTime: "{test_date.isoformat()}") {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result1
    assert "errors" not in result1

    # Test with only maxStartTime
    result2 = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                status(maxStartTime: "{test_date.isoformat()}") {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result2
    assert "errors" not in result2


@pytest.mark.asyncio
async def test_get_room_status_with_future_date_parameters() -> None:
    """Test room status field with future date parameters."""
    from datetime import datetime, timedelta
    from zoneinfo import ZoneInfo

    future_date = datetime.now(ZoneInfo("UTC")) + timedelta(days=30)
    query_min_end_time = future_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = future_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                name
                status(
                    minEndTime: "{query_min_end_time.isoformat()}"
                    maxStartTime: "{query_max_start_time.isoformat()}"
                ) {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result

    room_status = result["data"]["room"]["status"]
    assert "name" in room_status
    # For future dates with no scheduled cases, room should typically be CLOSED
    assert room_status["name"] in ["CLOSED", "IDLE"]


@pytest.mark.asyncio
async def test_get_room_status_timezone_handling() -> None:
    """Test that room status field handles different timezones correctly."""
    from datetime import datetime
    from zoneinfo import ZoneInfo

    # Test with different timezone
    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("America/Los_Angeles"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            room(id: "{harness.room_0.id}") {{
                id
                name
                status(
                    minEndTime: "{query_min_end_time.isoformat()}"
                    maxStartTime: "{query_max_start_time.isoformat()}"
                ) {{
                    name
                    since
                    calculatedAt
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result

    room_status = result["data"]["room"]["status"]
    assert "name" in room_status
    assert "since" in room_status
    assert "calculatedAt" in room_status
