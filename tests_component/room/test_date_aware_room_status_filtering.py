"""
Tests for date-aware room status filtering (RT-2172).

This test file specifically covers the fix for the "Hide Closed Rooms" filter
not updating when the date is changed. The fix ensures that room status filtering
and individual room status calculations use the provided date parameters instead
of always using the current date.
"""

import pytest
from datetime import datetime, timed<PERSON><PERSON>
from zoneinfo import ZoneInfo
from uuid import uuid4

from api_server.services.case.case_store import Case
from api_server.services.case.case_status import SCHEDULED, LIVE, COMPLETE
from tests_component import harness


@pytest.mark.asyncio
async def test_room_status_filtering_consistency_with_date_params() -> None:
    """
    Test that room status filtering and individual room status are consistent
    when date parameters are provided. This is the core issue from RT-2172.
    """
    # Create a test case for a specific date in the past
    test_date = datetime(2025, 4, 22, 10, 0, 0, tzinfo=ZoneInfo(harness.site_0.timezone))
    min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    max_start_time = test_date.replace(hour=23, minute=59, second=59, microsecond=999000)
    
    # Create a case that would be LIVE on the test date
    test_case = Case()
    test_case.case_id = str(uuid4())
    test_case.external_case_id = f"test_case_{test_case.case_id[:8]}"
    test_case.room_id = harness.room_0.id
    test_case.site_id = harness.site_0.id
    test_case.org_id = harness.org_0.id
    test_case.scheduled_start_time = test_date.replace(hour=9, minute=0)
    test_case.scheduled_end_time = test_date.replace(hour=11, minute=0)
    test_case.status = LIVE
    
    # Store the case
    if harness.async_session_maker:
        async with harness.async_session_maker() as session:
            await harness.case_store.create_case(test_case, session)
    
    try:
        # Query with date parameters - filter for IN_CASE rooms
        result = harness.service_account_client.execute_graphql(
            f"""
            query {{
                sites(siteIds: ["{harness.site_0.id}"]) {{
                    edges {{
                        node {{
                            id
                            rooms(
                                roomIds: ["{harness.room_0.id}"]
                                statusFilter: [IN_CASE]
                                minEndTime: "{min_end_time.isoformat()}"
                                maxStartTime: "{max_start_time.isoformat()}"
                            ) {{
                                edges {{
                                    node {{
                                        id
                                        name
                                        status(
                                            minEndTime: "{min_end_time.isoformat()}"
                                            maxStartTime: "{max_start_time.isoformat()}"
                                        ) {{
                                            name
                                            since
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
            """
        )
        
        # Should return the room because it's IN_CASE on the test date
        assert "data" in result
        assert "errors" not in result
        
        rooms = result["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
        assert len(rooms) == 1, "Room should be returned because it's IN_CASE on the test date"
        
        room = rooms[0]["node"]
        assert room["id"] == harness.room_0.id
        
        # CRITICAL: The room status should match the filter criteria
        # This was the bug - filtering would use test date but status would use current date
        assert room["status"]["name"] == "IN_CASE", (
            "Room status should be IN_CASE to match the filter criteria. "
            "This ensures filtering and status calculation use the same date."
        )
        
        # Query with same date parameters but different status filter - should return empty
        result_closed = harness.service_account_client.execute_graphql(
            f"""
            query {{
                sites(siteIds: ["{harness.site_0.id}"]) {{
                    edges {{
                        node {{
                            id
                            rooms(
                                roomIds: ["{harness.room_0.id}"]
                                statusFilter: [CLOSED]
                                minEndTime: "{min_end_time.isoformat()}"
                                maxStartTime: "{max_start_time.isoformat()}"
                            ) {{
                                edges {{
                                    node {{
                                        id
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
            """
        )
        
        # Should return empty because room is not CLOSED on the test date
        rooms_closed = result_closed["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
        assert len(rooms_closed) == 0, "Room should not be returned when filtering for CLOSED status"
        
    finally:
        # Clean up the test case
        if harness.async_session_maker:
            async with harness.async_session_maker() as session:
                await harness.case_store.delete_case(test_case.case_id, session)


@pytest.mark.asyncio
async def test_room_status_filtering_different_dates() -> None:
    """
    Test that room status filtering works correctly for different dates.
    This simulates the user changing dates in the Schedule view.
    """
    # Create cases for different dates
    date1 = datetime(2025, 4, 22, tzinfo=ZoneInfo(harness.site_0.timezone))
    date2 = datetime(2025, 4, 23, tzinfo=ZoneInfo(harness.site_0.timezone))
    
    # Case on date1 - room will be IN_CASE
    case1 = Case()
    case1.case_id = str(uuid4())
    case1.external_case_id = f"test_case_{case1.case_id[:8]}"
    case1.room_id = harness.room_0.id
    case1.site_id = harness.site_0.id
    case1.org_id = harness.org_0.id
    case1.scheduled_start_time = date1.replace(hour=10, minute=0)
    case1.scheduled_end_time = date1.replace(hour=12, minute=0)
    case1.status = LIVE
    
    # No case on date2 - room will be CLOSED
    
    if harness.async_session_maker:
        async with harness.async_session_maker() as session:
            await harness.case_store.create_case(case1, session)
    
    try:
        # Query for date1 - should find room with IN_CASE status
        min_end_time1 = date1.replace(hour=0, minute=0, second=0, microsecond=0)
        max_start_time1 = date1.replace(hour=23, minute=59, second=59, microsecond=999000)
        
        result1 = harness.service_account_client.execute_graphql(
            f"""
            query {{
                sites(siteIds: ["{harness.site_0.id}"]) {{
                    edges {{
                        node {{
                            rooms(
                                roomIds: ["{harness.room_0.id}"]
                                statusFilter: [IN_CASE]
                                minEndTime: "{min_end_time1.isoformat()}"
                                maxStartTime: "{max_start_time1.isoformat()}"
                            ) {{
                                edges {{
                                    node {{
                                        id
                                        status(
                                            minEndTime: "{min_end_time1.isoformat()}"
                                            maxStartTime: "{max_start_time1.isoformat()}"
                                        ) {{
                                            name
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
            """
        )
        
        rooms1 = result1["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
        assert len(rooms1) == 1, "Room should be found on date1 with IN_CASE status"
        assert rooms1[0]["node"]["status"]["name"] == "IN_CASE"
        
        # Query for date2 - should NOT find room with IN_CASE status
        min_end_time2 = date2.replace(hour=0, minute=0, second=0, microsecond=0)
        max_start_time2 = date2.replace(hour=23, minute=59, second=59, microsecond=999000)
        
        result2 = harness.service_account_client.execute_graphql(
            f"""
            query {{
                sites(siteIds: ["{harness.site_0.id}"]) {{
                    edges {{
                        node {{
                            rooms(
                                roomIds: ["{harness.room_0.id}"]
                                statusFilter: [IN_CASE]
                                minEndTime: "{min_end_time2.isoformat()}"
                                maxStartTime: "{max_start_time2.isoformat()}"
                            ) {{
                                edges {{
                                    node {{
                                        id
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
            """
        )
        
        rooms2 = result2["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
        assert len(rooms2) == 0, "Room should NOT be found on date2 with IN_CASE status"
        
        # Query for date2 with CLOSED filter - should find the room
        result3 = harness.service_account_client.execute_graphql(
            f"""
            query {{
                sites(siteIds: ["{harness.site_0.id}"]) {{
                    edges {{
                        node {{
                            rooms(
                                roomIds: ["{harness.room_0.id}"]
                                statusFilter: [CLOSED]
                                minEndTime: "{min_end_time2.isoformat()}"
                                maxStartTime: "{max_start_time2.isoformat()}"
                            ) {{
                                edges {{
                                    node {{
                                        id
                                        status(
                                            minEndTime: "{min_end_time2.isoformat()}"
                                            maxStartTime: "{max_start_time2.isoformat()}"
                                        ) {{
                                            name
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
            """
        )
        
        rooms3 = result3["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
        assert len(rooms3) == 1, "Room should be found on date2 with CLOSED status"
        assert rooms3[0]["node"]["status"]["name"] == "CLOSED"
        
    finally:
        # Clean up
        if harness.async_session_maker:
            async with harness.async_session_maker() as session:
                await harness.case_store.delete_case(case1.case_id, session)


@pytest.mark.asyncio
async def test_backward_compatibility_no_date_params() -> None:
    """
    Test that existing queries without date parameters still work correctly.
    This ensures we didn't break existing functionality.
    """
    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        rooms(statusFilter: [IDLE, CLOSED]) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                        since
                                        calculatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )
    
    assert "data" in result
    assert "errors" not in result
    
    rooms = result["data"]["sites"]["edges"][0]["node"]["rooms"]["edges"]
    # Should return rooms based on current date status
    for room_edge in rooms:
        room_status = room_edge["node"]["status"]
        assert room_status["name"] in ["IDLE", "CLOSED"]
        assert "since" in room_status
        assert "calculatedAt" in room_status
