from typing import Optional
from datetime import datetime, timed<PERSON><PERSON>
from zoneinfo import ZoneInfo
import pytest

from api_server.services.turnover.turnover_service import DEFAULT_MAX_TURNOVER_MINS
from api_server.services.case.case_store import Case
from api_server.services.case.case_status import SCHEDULED, LIVE, COMPLETE
from tests_component import harness


def test_query_site_requires_auth() -> None:
    result = harness.no_auth_client.execute_graphql(
        f"""
    query {{
        site(id: "{harness.site_0.id}") {{
                name
        }}
    }}
    """
    )
    assert result == {
        "data": {"site": None},
        "errors": [
            {
                "path": ["site"],
                "locations": [{"line": 3, "column": 9}],
                "message": "Not Authorized: No authorization found",
            }
        ],
    }


def test_query_sites_as_org_site_user() -> None:
    result = harness.site_0_scoped_client.execute_graphql(
        f"""
    query {{
        sites {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {"node": {"id": harness.site_0.id, "name": harness.site_0.name}},
                ]
            }
        }
    }


def test_query_sites_as_org_user() -> None:
    result = harness.surgeon_client.execute_graphql(
        f"""
    query {{
        sites {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )
    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {"node": {"id": harness.site_0.id, "name": harness.site_0.name}},
                    {"node": {"id": harness.site_1.id, "name": harness.site_1.name}},
                    {"node": {"id": harness.site_ca.id, "name": harness.site_ca.name}},
                ]
            }
        }
    }


def test_query_sites_as_universal_user() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {"node": {"id": harness.site_0.id, "name": harness.site_0.name}},
                    {"node": {"id": harness.site_1.id, "name": harness.site_1.name}},
                    {"node": {"id": harness.site_2.id, "name": harness.site_2.name}},
                    {"node": {"id": harness.site_ca.id, "name": harness.site_ca.name}},
                ]
            }
        }
    }


def test_query_site_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites(siteIds: ["{harness.site_0.id}", "{harness.site_1.id}"]) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {"node": {"id": harness.site_0.id, "name": harness.site_0.name}},
                    {"node": {"id": harness.site_1.id, "name": harness.site_1.name}},
                ]
            }
        }
    }


def test_query_site() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        site(id: "{harness.site_0.id}") {{
            id
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "site": {
                "id": harness.site_0.id,
            }
        }
    }


def test_query_site_id_rooms_idle() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
         sites(siteIds: ["{harness.site_0.id}"]) {{
            edges {{
                node {{
                    id
                    name
                    rooms (statusFilter: [IDLE]){{
                        edges {{
                        node {{
                            id
                            name
                            status {{
                                name
                              }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {
                        "node": {
                            "id": harness.site_0.id,
                            "name": harness.site_0.name,
                            "rooms": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": harness.room_0.id,
                                            "name": harness.room_0.name,
                                            "status": {"name": "IDLE"},
                                        }
                                    }
                                ]
                            },
                        }
                    }
                ]
            }
        }
    }


# When statusFileter is None or empty,
# all rooms under the site will be returned
def test_query_site_id_rooms_status_null() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites(siteIds: ["{harness.site_0.id}"]) {{
            edges {{
                node {{
                    id
                    name
                    rooms (statusFilter: []) {{
                        edges {{
                        node {{
                              id
                              name
                              status {{
                                name
                               }}
                             }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {
                        "node": {
                            "id": harness.site_0.id,
                            "name": harness.site_0.name,
                            "rooms": {
                                "edges": [
                                    {
                                        "node": {
                                            "id": harness.room_1.id,
                                            "name": harness.room_1.name,
                                            "status": {"name": "CLOSED"},
                                        }
                                    },
                                    {
                                        "node": {
                                            "id": harness.room_0.id,
                                            "name": harness.room_0.name,
                                            "status": {"name": "IDLE"},
                                        }
                                    },
                                ]
                            },
                        }
                    }
                ]
            }
        }
    }


def test_query_organization_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites(organizationIds: ["{harness.org_0.id}"]) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "sites": {
                "edges": [
                    {"node": {"id": harness.site_0.id, "name": harness.site_0.name}},
                    {"node": {"id": harness.site_1.id, "name": harness.site_1.name}},
                    {"node": {"id": harness.site_ca.id, "name": harness.site_ca.name}},
                ]
            }
        }
    }


def test_query_empty_organization_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites(organizationIds: []) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"sites": {"edges": []}}}


def test_query_site_by_nonexistent_organization_ids() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        sites(organizationIds: ["Non Existent IDS"]) {{
            edges {{
                node {{
                    name
                    id
                }}
            }}
        }}
    }}
    """
    )

    assert result == {"data": {"sites": {"edges": []}}}


def test_query_site_turnover_goals() -> None:
    result = harness.service_account_client.execute_graphql(
        f"""
    query {{
        site(id: "{harness.site_0.id}") {{
            turnoverGoals {{
                goalMinutes
                maxMinutes
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "site": {
                "turnoverGoals": {
                    "goalMinutes": None,
                    "maxMinutes": DEFAULT_MAX_TURNOVER_MINS,
                }
            }
        }
    }


def test_query_site_rooms_with_date_parameters() -> None:
    """
    Test that room status filtering uses provided date parameters instead of current date.
    This test verifies the fix for the "Hide Closed Rooms" filter bug.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED]
                            minEndTime: "{query_min_end_time.isoformat()}"
                            maxStartTime: "{query_max_start_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                        calculatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    sites = result["data"]["sites"]["edges"]
    assert len(sites) > 0

    assert "rooms" in sites[0]["node"]


def test_query_site_rooms_without_date_parameters_backward_compatibility() -> None:
    """
    Test that existing queries without date parameters still work (backward compatibility).
    """
    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(statusFilter: [IDLE]) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result


def test_query_site_rooms_date_parameters_timezone_handling() -> None:
    """
    Test that date parameters work correctly with different timezones.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("America/Los_Angeles"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED, IN_CASE, TURNOVER]
                            minEndTime: "{query_min_end_time.isoformat()}"
                            maxStartTime: "{query_max_start_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                        calculatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    sites = result["data"]["sites"]["edges"]
    assert len(sites) > 0


def test_query_site_rooms_date_parameters_edge_cases() -> None:
    """
    Test edge cases for date parameters.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED, IN_CASE, TURNOVER]
                            minEndTime: "{test_time.isoformat()}"
                            maxStartTime: "{test_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result


def test_query_site_rooms_partial_date_parameters() -> None:
    """
    Test behavior when only one date parameter is provided.
    This should fall back to current date behavior.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED]
                            minEndTime: "{test_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result

    result2 = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED]
                            maxStartTime: "{test_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result2
    assert "errors" not in result2


def test_query_site_rooms_future_date_parameters() -> None:
    """
    Test behavior when querying for future dates.
    """
    from datetime import datetime, timedelta
    from zoneinfo import ZoneInfo

    future_date = datetime.now(ZoneInfo("UTC")) + timedelta(days=30)
    query_min_end_time = future_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = future_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            statusFilter: [IDLE, CLOSED]
                            minEndTime: "{query_min_end_time.isoformat()}"
                            maxStartTime: "{query_max_start_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result


def test_query_site_rooms_no_status_filter_with_date_parameters() -> None:
    """
    Test that date parameters are ignored when no statusFilter is provided.
    This tests the conditional logic in the resolver.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms(
                            minEndTime: "{query_min_end_time.isoformat()}"
                            maxStartTime: "{query_max_start_time.isoformat()}"
                        ) {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    sites = result["data"]["sites"]["edges"]
    assert len(sites) > 0
    rooms = sites[0]["node"]["rooms"]["edges"]
    assert len(rooms) >= 0


def test_room_status_field_with_date_parameters() -> None:
    """
    Test that the room status field accepts and uses date parameters.
    This ensures room status is calculated for the specified date, not current date.
    """
    from datetime import datetime
    from zoneinfo import ZoneInfo

    test_date = datetime(2024, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    query_min_end_time = test_date.replace(hour=0, minute=0, second=0, microsecond=0)
    query_max_start_time = test_date.replace(
        hour=23, minute=59, second=59, microsecond=999000
    )

    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status(
                                        minEndTime: "{query_min_end_time.isoformat()}"
                                        maxStartTime: "{query_max_start_time.isoformat()}"
                                    ) {{
                                        name
                                        since
                                        calculatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    sites = result["data"]["sites"]["edges"]
    assert len(sites) > 0

    rooms = sites[0]["node"]["rooms"]["edges"]
    if len(rooms) > 0:
        room_status = rooms[0]["node"]["status"]
        assert "name" in room_status
        assert "since" in room_status
        assert "calculatedAt" in room_status


def test_room_status_field_without_date_parameters_backward_compatibility() -> None:
    """
    Test that the room status field works without date parameters (backward compatibility).
    """
    result = harness.service_account_client.execute_graphql(
        f"""
        query {{
            sites(siteIds: ["{harness.site_0.id}"]) {{
                edges {{
                    node {{
                        id
                        name
                        rooms {{
                            edges {{
                                node {{
                                    id
                                    name
                                    status {{
                                        name
                                        since
                                        calculatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
    )

    assert "data" in result
    assert "errors" not in result
    sites = result["data"]["sites"]["edges"]
    assert len(sites) > 0

    rooms = sites[0]["node"]["rooms"]["edges"]
    if len(rooms) > 0:
        room_status = rooms[0]["node"]["status"]
        assert "name" in room_status
        assert "since" in room_status
        assert "calculatedAt" in room_status


def test_update_site_turnover_goals() -> None:
    new_goal_minutes: Optional[int] = 45
    new_max_minutes = 50
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
        turnoverGoalsUpdate(input: {{
            orgId: "{harness.org_0.id}",
            siteId: "{harness.site_0.id}",
            goalMinutes: {new_goal_minutes},
            maxMinutes: {new_max_minutes}
        }}) {{
            success
            updatedTurnoverGoals {{
                goalMinutes
                maxMinutes
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "turnoverGoalsUpdate": {
                "success": True,
                "updatedTurnoverGoals": {
                    "goalMinutes": new_goal_minutes,
                    "maxMinutes": new_max_minutes,
                },
            }
        }
    }

    new_goal_minutes = None
    new_max_minutes = 55
    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
        turnoverGoalsUpdate(input: {{
            orgId: "{harness.org_0.id}",
            siteId: "{harness.site_0.id}",
            goalMinutes: null,
            maxMinutes: {new_max_minutes}
        }}) {{
            success
            updatedTurnoverGoals {{
                goalMinutes
                maxMinutes
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "turnoverGoalsUpdate": {
                "success": True,
                "updatedTurnoverGoals": {
                    "goalMinutes": new_goal_minutes,
                    "maxMinutes": new_max_minutes,
                },
            }
        }
    }

    result = harness.service_account_client.execute_graphql(
        f"""
    mutation {{
        turnoverGoalsUpdate(input: {{
            orgId: "{harness.org_0.id}",
            siteId: "{harness.site_0.id}",
            goalMinutes: null,
            maxMinutes: null
        }}) {{
            success
            updatedTurnoverGoals {{
                goalMinutes
                maxMinutes
            }}
        }}
    }}
    """
    )

    assert result == {
        "data": {
            "turnoverGoalsUpdate": {
                "success": True,
                "updatedTurnoverGoals": {
                    "goalMinutes": new_goal_minutes,
                    "maxMinutes": DEFAULT_MAX_TURNOVER_MINS,
                },
            }
        }
    }
