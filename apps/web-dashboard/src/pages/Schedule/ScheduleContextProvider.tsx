import {
  ReactN<PERSON>,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from 'react'
import { useLocation } from 'react-router'
import { toast } from 'react-toastify'

import { useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { useLocalStorageState } from '@apella/hooks'
import {
  CaseMatchingStatus,
  TurnoverLabel,
} from 'src/__generated__/globalTypes'
import { useDisplayMode } from 'src/Contexts'
import { useScheduleMetrics } from 'src/modules/daily-metrics/hooks/useScheduleMetrics'
import { DailyMetrics } from 'src/modules/daily-metrics/types'
import {
  EditMode,
  EditScheduleState,
} from 'src/pages/Schedule/EditSchedule/types'
import { useEditScheduleOnClick } from 'src/pages/Schedule/EditSchedule/useEditScheduleOnClick'
import { useScheduleFilterContext } from 'src/pages/Schedule/ScheduleFilterContext'
import {
  SCHEDULE_TOAST_ID,
  ScheduleViewOnClickHandler,
  TimelineDateTimeBounds,
  TimelineRoom,
  ViewRoomMetric,
} from 'src/pages/Schedule/types'
import {
  TimelineState,
  useTimelineState,
} from 'src/pages/Schedule/useTimelineState'
import { ApellaCase, Turnover } from 'src/pages/types'
import { LocationPath } from 'src/router/types'

import { EVENTS } from '../../utils/analyticsEvents'
import { getAnalyticsAddtlTurnoverData } from '../../utils/turnovers'
import { useOpenVideoBlade } from '../../utils/useOpenVideoBlade'
import { GetTurnoverLabels, GetTurnoverLabelsVariables } from './__generated__'
import { GET_TURNOVER_LABELS } from './queries'
import { useTimelineHourBounds } from './useTimelineHourBounds'

interface ScheduleContext {
  editScheduleState: EditScheduleState
  filteredTimelineState: TimelineState
  isAllEditPage?: boolean
  isEditingSchedule: boolean
  isFullscreen: boolean
  onClick: ScheduleViewOnClickHandler
  scheduleMetrics?: ReturnType<typeof useScheduleMetrics>
  setEditScheduleState: (state: EditScheduleState) => void
  showPanel?: boolean
  timelineHourBounds?: TimelineDateTimeBounds
  timelineState: TimelineState
  turnoverLabels: TurnoverLabel[]
}

export const ScheduleContext = createContext<ScheduleContext>({
  editScheduleState: {
    currentEdits: [],
    mode: EditMode.PickCase,
  },
  onClick: () => { },
  setEditScheduleState: () => { },
  showPanel: false,
  isFullscreen: false,
  timelineState: {
    getCases: () => { },
    isLoading: true,
    maxTime: '',
    minTime: '',
    rooms: [],
    totalRoomsBeforeFiltering: 0,
  },
  filteredTimelineState: {
    getCases: () => { },
    isLoading: true,
    maxTime: '',
    minTime: '',
    rooms: [],
    totalRoomsBeforeFiltering: 0,
  },
  isEditingSchedule: false,
  turnoverLabels: [],
})

export const useScheduleContext = () => useContext(ScheduleContext)

const showPanelRoutes: Partial<{ [key: string]: boolean }> = {
  [LocationPath.EditSchedule]: true,
}

export const ScheduleProvider = ({ children }: { children: ReactNode }) => {
  const {
    minTime,
    maxTime,
    siteIds,
    showClosedRooms,
    sortKeys,
    viewRoomsState,
    selectedSurgeons,
    roomIds,
    timeRange,
  } = useScheduleFilterContext()

  const location = useLocation()

  const showPanel = showPanelRoutes[location.pathname]

  // Query for all case matching statuses only when the edit schedule panel is open.
  // Don't query for all case matching statuses by default to improve the page load
  // latency for the main Schedule page load.
  const caseMatchingStatuses = showPanel
    ? new Set(Object.values(CaseMatchingStatus))
    : undefined

  const timelineState = useTimelineState({
    minTime,
    maxTime,
    options: {
      siteIds: siteIds,
      includeTurnovers: true,
      caseMatchingStatuses,
      showClosedRooms: showClosedRooms,
      sortKeys,
    },
  })

  // The rooms that will be used to calculate the Schedule Metrics.
  const scheduleMetricsRooms = useMemo(() => {
    let newRooms = timelineState.rooms

    // Filter down by rooms
    if (roomIds !== undefined && roomIds.length) {
      newRooms = newRooms.filter((r) => roomIds.includes(r.id))
    }

    return newRooms
  }, [roomIds, timelineState.rooms])

  const scheduleMetrics = useScheduleMetrics({
    minTime,
    apellaCases: scheduleMetricsRooms.flatMap((r) => r.cases),
  })

  const { data: GetTurnoverLabelsData } = useQuery<
    GetTurnoverLabels,
    GetTurnoverLabelsVariables
  >(GET_TURNOVER_LABELS, {
    variables: { query: { type: 'exclusion' } }, // Only get exclusion labels
  })

  const turnoverLabels = GetTurnoverLabelsData?.turnoverLabels ?? []

  // Further filter down the rooms used to calculate the Schedule Metrics.
  // These filters don't affect the schedule metrics, but do affect the room shown in Schedule.
  const filteredTimelineState = filterTimelineState({
    scheduleMetricsRooms,
    viewRoomsState,
    scheduleMetrics,
    selectedSurgeons,
    timelineState,
  })

  const mergedPrimeTime = getPrimeTime(filteredTimelineState)

  const timelineHourBounds = useTimelineHourBounds({
    minTime,
    maxTime,
    cases: filteredTimelineState.rooms.flatMap((r) => r.cases),
    calculationMethod: timeRange,
    primeTime: mergedPrimeTime,
  })

  const { isTvModeEnabled: isFullscreen } = useDisplayMode()

  const [editScheduleState, setEditScheduleState] =
    useLocalStorageState<EditScheduleState>('editScheduleState', {
      mode: EditMode.PickCase,
      currentEdits: [],
    })

  const handleOpenVideoBlade = useOpenVideoBlade({ appendParams: true })

  const editScheduleOnClick = useEditScheduleOnClick(
    editScheduleState,
    setEditScheduleState
  )

  const onClickSchedule = useCallback(
    (
      roomId: string,
      clickTime: DateTime,
      apellaCase?: ApellaCase,
      turnover?: Turnover
    ) => {
      handleOpenVideoBlade(roomId, {
        apellaCase,
        turnover,
        time: clickTime,
        analyticsAddtlData: getAnalyticsAddtlTurnoverData(turnover),
        analyticsEvent: EVENTS.SCHEDULE_PAGE_OPEN_VIDEO_BLADE,
      })
    },
    [handleOpenVideoBlade]
  )

  useEffect(() => {
    toast.dismiss(SCHEDULE_TOAST_ID)
  }, [location])

  const onClick = showPanel ? editScheduleOnClick : onClickSchedule

  return (
    <ScheduleContext.Provider
      value={{
        editScheduleState,
        onClick,
        setEditScheduleState,
        showPanel,
        isFullscreen,
        timelineState,
        isEditingSchedule: !!showPanel,
        filteredTimelineState,
        scheduleMetrics,
        timelineHourBounds,
        turnoverLabels,
      }}
    >
      {children}
    </ScheduleContext.Provider>
  )
}

export const filterTimelineState = ({
  scheduleMetricsRooms,
  viewRoomsState,
  scheduleMetrics,
  selectedSurgeons,
  timelineState,
}: {
  scheduleMetricsRooms: TimelineRoom[]
  viewRoomsState?: ViewRoomMetric
  scheduleMetrics: Pick<
    DailyMetrics,
    | 'firstCaseStarts'
    | 'stillOpen'
    | 'openPast15'
    | 'openPast17'
    | 'openPast19'
    | 'openToday'
    | 'totalCases'
    | 'completedCases'
  >
  selectedSurgeons: string[] | undefined
  timelineState: TimelineState
}) => {
  const rooms = scheduleMetricsRooms.filter((room) => {
    const matchesViewState =
      !viewRoomsState ||
      !scheduleMetrics[viewRoomsState].length ||
      scheduleMetrics[viewRoomsState].includes(room.id)
    const matchesSurgeons =
      !selectedSurgeons ||
      selectedSurgeons.length === 0 ||
      room.cases.some(
        (c) =>
          c.case &&
          c.case.staff.some((staff) => selectedSurgeons.includes(staff.id))
      )
    return matchesViewState && matchesSurgeons
  })

  // Return the new state
  return {
    ...timelineState,
    rooms: rooms,
  }
}

export const getPrimeTime = (filteredTimelineState: TimelineState) => {
  const times = filteredTimelineState.rooms.reduce(
    (acc: { startTimes: DateTime[]; endTimes: DateTime[] }, room) => {
      if (room.primeTime === null) {
        return acc
      }

      const startTime = DateTime.fromFormat(
        room.primeTime.startTime,
        'HH:mm:ss'
      )
      const endTime = DateTime.fromFormat(room.primeTime.endTime, 'HH:mm:ss')

      acc.startTimes.push(startTime)
      acc.endTimes.push(endTime)

      return acc
    },
    { startTimes: [], endTimes: [] }
  )

  if (times.startTimes.length === 0 || times.endTimes.length === 0) {
    return undefined
  }

  const minStartTime = DateTime.min(...times.startTimes)
  const maxEndTime = DateTime.max(...times.endTimes)

  return {
    startTime: minStartTime,
    endTime: maxEndTime,
  }
}
