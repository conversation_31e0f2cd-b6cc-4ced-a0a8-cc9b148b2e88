import { DateTime } from 'luxon'

import {
  CaseStatusName,
  CaseType,
  RoomStatusName,
} from 'src/__generated__/globalTypes'
import { DailyMetrics } from 'src/modules/daily-metrics/types'
import { TimelineRoom } from 'src/pages/Schedule/types'

import { filterTimelineState, getPrimeTime } from './ScheduleContextProvider'

const ROOM_1 = 'room1'
const ROOM_2 = 'room2'
const mockRooms: TimelineRoom[] = [
  {
    id: ROOM_1,
    cases: [
      {
        id: 'case1',
        case: {
          id: 'case1',
          staff: [
            {
              id: 'surgeon1',
              displayName: 'Dr. Surgeon1',
              firstName: 'John',
              lastName: 'Doe',
              role: 'Surgeon',
            },
          ],
          caseFlags: [],
          procedures: [],
          room: { id: ROOM_2, name: 'Room 2' },
          scheduledEndTime: DateTime.now(),
          scheduledStartTime: DateTime.now(),
        },
        room: { id: ROOM_1, name: 'Room 1' },
        site: { id: 'site1', name: 'Site 1' },
        startTime: DateTime.now(),
        status: {
          name: CaseStatusName.ACTUAL,
          since: DateTime.now(),
        },
        type: CaseType.COMPLETE,
      },
    ],
    primeTime: {
      startTime: '08:00:00',
      endTime: '12:00:00',
    },
    since: DateTime.now(),
    site: {
      id: '',
      name: '',
    },
    sortKey: null,
    status: {
      name: RoomStatusName.CLOSED,
      since: DateTime.now(),
    },
    turnovers: [],
    name: '',
  },
  {
    id: ROOM_2,
    cases: [
      {
        id: 'case2',
        case: {
          id: 'case2',
          staff: [
            {
              id: 'surgeon2',
              displayName: 'Dr. Surgeon2',
              firstName: 'Jane',
              lastName: 'Smith',
              role: 'Surgeon',
            },
          ],
          caseFlags: [],
          procedures: [],
          room: { id: ROOM_2, name: 'Room 2' },
          scheduledEndTime: DateTime.now(),
          scheduledStartTime: DateTime.now(),
        },
        room: { id: ROOM_2, name: 'Room 2' },
        site: { id: 'site1', name: 'Site 1' },
        startTime: DateTime.now(),
        status: {
          name: CaseStatusName.ACTUAL,
          since: DateTime.now(),
        },
        type: CaseType.COMPLETE,
      },
    ],
    primeTime: {
      startTime: '09:00:00',
      endTime: '13:00:00',
    },
    since: DateTime.now(),
    site: {
      id: '',
      name: '',
    },
    sortKey: null,
    status: {
      name: RoomStatusName.CLOSED,
      since: DateTime.now(),
    },
    turnovers: [],
    name: '',
  },
]

const mockTimelineState = {
  getCases: vitest.fn(),
  isLoading: false,
  maxTime: '',
  minTime: '',
  rooms: mockRooms,
  totalRoomsBeforeFiltering: mockRooms.length,
}

describe('filterTimelineState', () => {
  const mockScheduleMetrics: Pick<
    DailyMetrics,
    | 'firstCaseStarts'
    | 'stillOpen'
    | 'openPast15'
    | 'openPast17'
    | 'openPast19'
    | 'openToday'
    | 'totalCases'
    | 'completedCases'
  > = {
    firstCaseStarts: [ROOM_1],
    stillOpen: [ROOM_1, ROOM_2],
    openPast15: [],
    openPast17: [],
    openPast19: [],
    openToday: [],
    totalCases: 0,
    completedCases: 0,
  }

  it('filters rooms based on viewRoomsState', () => {
    const result = filterTimelineState({
      scheduleMetricsRooms: mockRooms,
      viewRoomsState: 'stillOpen',
      scheduleMetrics: mockScheduleMetrics,
      selectedSurgeons: undefined,
      timelineState: mockTimelineState,
    })

    expect(result.rooms).toHaveLength(2)
  })

  it('filters rooms based on selectedSurgeons', () => {
    const result = filterTimelineState({
      scheduleMetricsRooms: mockRooms,
      viewRoomsState: undefined,
      scheduleMetrics: mockScheduleMetrics,
      selectedSurgeons: ['surgeon1'],
      timelineState: mockTimelineState,
    })

    expect(result.rooms).toHaveLength(1)
    expect(result.rooms[0].id).toBe(ROOM_1)
  })

  it('filters rooms based on both viewRoomsState and selectedSurgeons', () => {
    const result = filterTimelineState({
      scheduleMetricsRooms: mockRooms,
      viewRoomsState: 'stillOpen',
      scheduleMetrics: mockScheduleMetrics,
      selectedSurgeons: ['surgeon2'],
      timelineState: mockTimelineState,
    })

    expect(result.rooms).toHaveLength(1)
    expect(result.rooms[0].id).toBe(ROOM_2)
  })

  it('returns all rooms if no filters are applied', () => {
    const result = filterTimelineState({
      scheduleMetricsRooms: mockRooms,
      viewRoomsState: undefined,
      scheduleMetrics: mockScheduleMetrics,
      selectedSurgeons: undefined,
      timelineState: mockTimelineState,
    })

    expect(result.rooms).toHaveLength(2)
  })
})
describe('getPrimeTime', () => {
  it('returns undefined if no rooms have primeTime', () => {
    const filteredTimelineState = {
      ...mockTimelineState,
      rooms: mockTimelineState.rooms.map((r) => ({ ...r, primeTime: null })),
    }

    const result = getPrimeTime(filteredTimelineState)
    expect(result).toBeUndefined()
  })

  it('returns the correct primeTime for rooms with primeTime', () => {
    const result = getPrimeTime(mockTimelineState)
    expect(result).toEqual({
      startTime: DateTime.fromFormat('08:00:00', 'HH:mm:ss'),
      endTime: DateTime.fromFormat('13:00:00', 'HH:mm:ss'),
    })
  })

  it('handles mixed rooms with and without primeTime', () => {
    const filteredTimelineState = {
      ...mockTimelineState,
      rooms: [
        ...mockTimelineState.rooms,
        { ...mockTimelineState.rooms[1], id: 'room3', primeTime: null },
      ],
    }

    const result = getPrimeTime(filteredTimelineState)
    expect(result).toEqual({
      startTime: DateTime.fromFormat('08:00:00', 'HH:mm:ss'),
      endTime: DateTime.fromFormat('13:00:00', 'HH:mm:ss'),
    })
  })
})
