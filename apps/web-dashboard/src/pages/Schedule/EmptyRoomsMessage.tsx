import { useMemo } from 'react'

import { DateTime } from 'luxon'

import {
  But<PERSON>,
  FlexContainer,
  H4,
  P2,
  remSpacing,
  Tile
} from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

import { useScheduleFilterContext } from './ScheduleFilterContext'

interface EmptyRoomsMessageProps {
  totalRoomsBeforeFiltering: number
  isLoading: boolean
}

/**
 * Component that displays helpful messages when no rooms are shown,
 * particularly when "Hide Closed Rooms" filter results in empty results.
 */
export const EmptyRoomsMessage = ({
  totalRoomsBeforeFiltering,
  isLoading
}: EmptyRoomsMessageProps) => {
  const { timezone } = useTimezone()
  const {
    minTime,
    showClosedRooms,
    onToggleShowClosedRooms,
  } = useScheduleFilterContext()

  const messageInfo = useMemo(() => {
    if (isLoading) {
      return null
    }

    const selectedDate = DateTime.fromISO(minTime).setZone(timezone)
    const isToday = selectedDate.hasSame(DateTime.now().setZone(timezone), 'day')
    const isWeekend = selectedDate.weekday >= 6 // Saturday = 6, Sunday = 7
    const dayName = selectedDate.toFormat('cccc') // Full day name
    const dateFormatted = selectedDate.toFormat('MMMM d, yyyy')

    // If showing closed rooms and still no rooms, it's a different issue
    if (showClosedRooms) {
      return {
        type: 'no-rooms' as const,
        title: 'No rooms found',
        message: `No rooms are available for ${isToday ? 'today' : dateFormatted}.`,
        showToggle: false,
      }
    }

    // If hiding closed rooms and no rooms shown, but there were rooms before filtering
    if (!showClosedRooms && totalRoomsBeforeFiltering > 0) {
      return {
        type: 'all-closed' as const,
        title: 'All rooms are closed',
        message: isToday
          ? 'All rooms are currently closed. You can show closed rooms to see the full schedule.'
          : `All rooms are closed on ${dayName}, ${dateFormatted}. ${isWeekend ? 'This is common on weekends. ' : ''
          }You can show closed rooms to see the full schedule.`,
        showToggle: true,
      }
    }

    // If hiding closed rooms and no rooms shown, and no rooms before filtering
    if (!showClosedRooms && totalRoomsBeforeFiltering === 0) {
      return {
        type: 'no-rooms' as const,
        title: 'No rooms found',
        message: `No rooms are available for ${isToday ? 'today' : dateFormatted}.`,
        showToggle: false,
      }
    }

    return null
  }, [isLoading, minTime, timezone, showClosedRooms, totalRoomsBeforeFiltering])

  if (!messageInfo) {
    return null
  }

  const handleShowClosedRooms = () => {
    onToggleShowClosedRooms(true)
  }

  return (
    <div
      css={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '300px',
        padding: remSpacing.large,
      }}
    >
      <Tile
        css={{
          maxWidth: '600px',
          textAlign: 'center',
        }}
        gutter={remSpacing.large}
      >
        <FlexContainer
          direction="column"
          gap={remSpacing.medium}
          alignItems="center"
        >
          <H4>{messageInfo.title}</H4>
          <P2>{messageInfo.message}</P2>
          {messageInfo.showToggle && (
            <Button
              appearance="button"
              onClick={handleShowClosedRooms}
              size="md"
            >
              Show closed rooms
            </Button>
          )}
        </FlexContainer>
      </Tile>
    </div>
  )
}
