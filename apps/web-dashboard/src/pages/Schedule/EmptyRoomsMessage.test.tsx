import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DateTime } from 'luxon'
import { vi } from 'vitest'

import { ThemeProvider } from '@emotion/react'
import { theme } from '@apella/component-library'
import { TimezoneProvider } from 'src/Contexts'

import { EmptyRoomsMessage } from './EmptyRoomsMessage'
import { ScheduleFilterContext } from './ScheduleFilterContext'

// Mock the context values
const createMockContext = (overrides = {}) => ({
  sites: [],
  rooms: [],
  onChangeSurgeons: vi.fn(),
  maxTime: '',
  minTime: DateTime.now().toISO(),
  onChangeDate: vi.fn(),
  onChangeSites: vi.fn(),
  onChangeRooms: vi.fn(),
  onToggleScheduled: vi.fn(),
  showResetFiltersButton: false,
  showScheduled: true,
  onChangeDailyMetric: vi.fn(),
  resetFilters: vi.fn(),
  onToggleShowClosedRooms: vi.fn(),
  onToggleShowMetrics: vi.fn(),
  showClosedRooms: true,
  showMetrics: true,
  sortKeys: [],
  onToggleSortKey: vi.fn(),
  onToggleFilters: vi.fn(),
  timeRange: 'DayBound' as const,
  ...overrides,
})

const renderWithProviders = (
  component: React.ReactElement,
  contextOverrides = {},
  timezone = 'America/New_York'
) => {
  const mockContext = createMockContext(contextOverrides)

  return render(
    <ThemeProvider theme={theme}>
      <TimezoneProvider timezone={timezone}>
        <ScheduleFilterContext.Provider value={mockContext}>
          {component}
        </ScheduleFilterContext.Provider>
      </TimezoneProvider>
    </ThemeProvider>
  )
}

describe('EmptyRoomsMessage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not render when loading', () => {
    const { container } = renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={0} isLoading={true} />
    )

    expect(container.firstChild).toBeNull()
  })

  it('should show "No rooms found" when showClosedRooms is true and no rooms exist', () => {
    const today = DateTime.now().toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={0} isLoading={false} />,
      {
        minTime: today,
        showClosedRooms: true,
      }
    )

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
    expect(screen.queryByText('Show closed rooms')).not.toBeInTheDocument()
  })

  it('should show "All rooms are closed" message when hiding closed rooms and rooms exist before filtering', () => {
    const today = DateTime.now().toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={5} isLoading={false} />,
      {
        minTime: today,
        showClosedRooms: false,
      }
    )

    expect(screen.getByText('All rooms are closed')).toBeInTheDocument()
    expect(screen.getByText(/All rooms are currently closed/)).toBeInTheDocument()
    expect(screen.getByText('Show closed rooms')).toBeInTheDocument()
  })

  it('should show weekend-specific message for weekend dates', () => {
    // Create a Saturday date in the America/New_York timezone
    const saturday = DateTime.fromObject(
      { year: 2025, month: 6, day: 7, hour: 12 },
      { zone: 'America/New_York' }
    ).toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={3} isLoading={false} />,
      {
        minTime: saturday,
        showClosedRooms: false,
      },
      'America/New_York'
    )

    expect(screen.getByText('All rooms are closed')).toBeInTheDocument()
    // Check for the weekend message in the text content
    expect(screen.getByText(/This is common on weekends/)).toBeInTheDocument()
    expect(screen.getByText('Show closed rooms')).toBeInTheDocument()
  })

  it('should call onToggleShowClosedRooms when "Show closed rooms" button is clicked', async () => {
    const user = userEvent.setup()
    const mockToggle = vi.fn()
    const today = DateTime.now().toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={3} isLoading={false} />,
      {
        minTime: today,
        showClosedRooms: false,
        onToggleShowClosedRooms: mockToggle,
      }
    )

    const button = screen.getByText('Show closed rooms')
    await user.click(button)

    expect(mockToggle).toHaveBeenCalledWith(true)
  })

  it('should show formatted date for non-today dates', () => {
    const futureDate = DateTime.now().plus({ days: 7 }).toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={2} isLoading={false} />,
      {
        minTime: futureDate,
        showClosedRooms: false,
      }
    )

    expect(screen.getByText('All rooms are closed')).toBeInTheDocument()
    // Should contain a formatted date (month name and year)
    expect(screen.getByText(/\w+ \d+, \d{4}/)).toBeInTheDocument()
  })

  it('should handle timezone correctly', () => {
    const date = DateTime.fromISO('2025-04-22T10:00:00.000Z').toISO()

    renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={1} isLoading={false} />,
      {
        minTime: date,
        showClosedRooms: false,
      },
      'America/Los_Angeles' // Different timezone
    )

    expect(screen.getByText('All rooms are closed')).toBeInTheDocument()
  })

  it('should not render when there are no issues (rooms exist and not loading)', () => {
    // This case shouldn't happen in practice, but testing the edge case
    // When showClosedRooms is true and totalRoomsBeforeFiltering > 0,
    // but we're still calling EmptyRoomsMessage, it means no rooms are visible
    // which should show the "No rooms found" message
    const { container } = renderWithProviders(
      <EmptyRoomsMessage totalRoomsBeforeFiltering={0} isLoading={false} />,
      {
        showClosedRooms: true, // Showing closed rooms but no rooms before filtering
      }
    )

    // Should show "No rooms found" message
    expect(container.firstChild).not.toBeNull()
    expect(screen.getByText('No rooms found')).toBeInTheDocument()
  })
})
