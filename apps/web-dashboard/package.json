{"name": "web-dashboard", "main": "src/index.ts", "version": "0.0.0", "type": "module", "description": "Apella Customer Dashboard", "author": "<PERSON>", "license": "UNLICENSED", "private": true, "browserslist": ["last 2 chrome versions", "last 2 safari versions", "last 2 firefox versions", "last 2 edge versions", "> 0.5%"], "scripts": {"build": "NODE_ENV=production vite build", "clean": "yarn graphql:clean && rm -rf dist node_modules tsconfig.tsbuildinfo", "circular-dependency:check": "madge --circular src/", "dev": "concurrently --names \"server,graphql\" -c \"bgBlue.bold,bgMagenta.bold\" \"vite serve --open\" \"yarn graphql:gen:watch\" --kill-others", "dev:local": "export APELLA_ENV=localhost; concurrently --names \"server,graphql\" -c \"bgBlue.bold,bgMagenta.bold\" \"vite serve --open\" \"yarn graphql:gen:local --watch\" --kill-others", "graphql:clean": "find ./src -name '__generated__' -type d -exec rm -rf {} +", "graphql:gen": "graphql-codegen --config codegen.ts", "graphql:gen:local": "APELLA_ENV=localhost yarn graphql:gen --localSchemaFile=./schema.graphql", "graphql:gen:watch": "yarn graphql:gen --watch", "lint": "eslint --max-warnings=0 ./src && prettier --check ./src", "format": "eslint --cache --max-warnings=0 --fix ./src && prettier --cache --write ./src", "sourcemaps:sentry": "yarn sourcemaps:release && yarn sourcemaps:upload && yarn sourcemaps:commits", "sourcemaps:commits": "sentry-cli releases set-commits \"$SENTRY_RELEASE\" --auto", "sourcemaps:release": "sentry-cli releases new $SENTRY_RELEASE --finalize", "sourcemaps:upload": "sentry-cli releases files $SENTRY_RELEASE upload-sourcemaps ./dist/", "storybook": "storybook dev -p 9001 -c .storybook", "build-storybook": "storybook build -c .storybook", "test": "vitest run", "test:silent": "vitest run --silent", "test:update-snapshots": "yarn test:silent --update", "test:watch": "vitest watch", "tsc": "tsc --build", "validate:fix-no-test": "yarn format; yarn tsc; yarn circular-dependency:check", "validate:fix": "yarn validate:fix-no-test; yarn test:update-snapshots"}, "nx": {"targets": {"build": {"dependsOn": ["^build", "tsc", "graphql:gen"]}, "dev": {"dependsOn": ["graphql:gen"]}, "dev:local": {"dependsOn": ["graphql:gen:local"]}, "graphql:gen": {"cache": true, "inputs": ["default"]}, "test": {"dependsOn": ["graphql:gen"]}, "test:update-snapshots": {"dependsOn": ["graphql:gen"]}, "tsc": {"dependsOn": ["graphql:gen"], "inputs": ["default", "generated", "^default"]}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/libs/eslint-config-custom/**/*"]}}}, "dependencies": {"@amplitude/analytics-browser": "^2.17.6", "@apella/component-library": "*", "@apella/hooks": "*", "@apella/logger": "*", "@apella/react-router-storybook": "*", "@apollo/client": "^3.13.8", "@auth0/auth0-react": "^2.3.0", "@auth0/auth0-spa-js": "^2.1.3", "@conform-to/react": "^1.5.1", "@conform-to/yup": "^1.5.1", "@cubejs-client/core": "=1.3.15", "@cubejs-client/react": "=1.3.15", "@datadog/browser-rum": "^6.6.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@sentry/browser": "^9.19.0", "@sentry/react": "^9.19.0", "@storybook/test": "^8.6.14", "color-contrast": "^1.0.0", "emotion-reset": "^3.0.1", "formik": "^2.4.6", "identity-obj-proxy": "^3.0.0", "launchdarkly-react-client-sdk": "^3.7.0", "libphonenumber-js": "^1.12.8", "lodash": "^4.17.21", "luxon": "^3.6.1", "papaparse": "^5.5.2", "polished": "^4.3.1", "qs": "^6.13.0", "react": "^19.0.0", "react-csv": "^2.2.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-loading-skeleton": "^3.5.0", "react-page-visibility": "^7.0.0", "react-router": "^7.6.0", "react-scroll-sync": "^0.11.3", "react-toastify": "^11.0.5", "react-virtuoso": "^4.12.7", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@apella/eslint-config-custom": "*", "@apollo/react-testing": "^4.0.0", "@chromatic-com/storybook": "^3.2.6", "@emotion/babel-plugin": "^11.13.5", "@emotion/jest": "^11.13.0", "@faker-js/faker": "^9.8.0", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@graphql-codegen/typed-document-node": "^3.0.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@parcel/watcher": "^2.5.1", "@sentry/cli": "^2.45.0", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-storysource": "^8.6.14", "@storybook/builder-vite": "^8.6.14", "@storybook/core": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/theming": "^8.6.14", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/hoist-non-react-statics": "^3.3.6", "@types/luxon": "^2.0.4", "@types/papaparse": "^5.3.16", "@types/qs": "^6.14.0", "@types/react": "^19.0.0", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19.0.0", "@types/react-page-visibility": "^6.4.1", "@types/react-scroll-sync": "^0.9.0", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "eslint": "^9.27.0", "graphql": "^16.8.1", "graphql-codegen-typescript-mock-data": "^3.7.0", "jsdom": "^26.1.0", "madge": "^6.1.0", "mockdate": "^3.0.5", "msw": "^2.8.4", "msw-storybook-addon": "^2.0.4", "prettier": "^3.5.3", "react-refresh": "^0.17.0", "storybook": "^8.6.14", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3", "vite-plugin-html": "^3.2.2", "vitest": "^3.1.4", "vitest-fetch-mock": "^0.4.5"}, "msw": {"workerDirectory": ["public"]}, "overrides": {"react-is": "^19.0.0"}}