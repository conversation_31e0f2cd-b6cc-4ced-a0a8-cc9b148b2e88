"""
Unit tests for date-aware room status calculation (RT-2172).

These tests verify that the room status calculation logic correctly uses
provided date parameters instead of always using the current date.
"""

import pytest
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo
from typing import List
from unittest.mock import AsyncMock

from api_server.services.apella_case.apella_case_models import ApellaCase, CaseType
from api_server.services.room.room_service import RoomService, RoomStatusName
from tests.harness import harness


def mock_apella_case(
    start_time: datetime,
    end_time: datetime,
    case_type: CaseType = CaseType.LIVE,
) -> ApellaCase:
    """Helper function to create mock ApellaCase objects."""
    case = ApellaCase()
    case.id = "test_case_id"
    case.start_time = start_time
    case.end_time = end_time
    case.type = case_type
    return case


@pytest.mark.asyncio
async def test_room_status_calculation_with_specific_date() -> None:
    """
    Test that room status calculation uses the provided computed_at time
    instead of current time for determining status.
    """
    room_service = RoomService()
    
    # Create a scenario where room status depends on the calculation time
    base_time = datetime(2025, 4, 22, 10, 0, 0, timezone.utc)
    
    # Case that runs from 9 AM to 11 AM
    case_start = base_time.replace(hour=9)
    case_end = base_time.replace(hour=11)
    live_case = mock_apella_case(case_start, case_end, CaseType.LIVE)
    
    cases: List[ApellaCase] = [live_case]
    
    # Test 1: Calculate status at 10 AM (during case) - should be IN_CASE
    calculation_time_during = base_time.replace(hour=10)
    status_during = await room_service.get_room_status(cases, calculation_time_during)
    
    assert status_during.name == RoomStatusName.IN_CASE
    assert status_during.since == case_start
    assert status_during.computed_at == calculation_time_during
    assert status_during.in_progress_apella_case == live_case
    
    # Test 2: Calculate status at 8 AM (before case) - should be IDLE
    calculation_time_before = base_time.replace(hour=8)
    status_before = await room_service.get_room_status(cases, calculation_time_before)
    
    assert status_before.name == RoomStatusName.IDLE
    assert status_before.computed_at == calculation_time_before
    assert status_before.in_progress_apella_case is None
    
    # Test 3: Calculate status at 12 PM (after case) - should be IDLE
    calculation_time_after = base_time.replace(hour=12)
    status_after = await room_service.get_room_status(cases, calculation_time_after)
    
    assert status_after.name == RoomStatusName.IDLE
    assert status_after.computed_at == calculation_time_after
    assert status_after.in_progress_apella_case is None


@pytest.mark.asyncio
async def test_room_status_calculation_different_dates() -> None:
    """
    Test that room status calculation works correctly for different dates.
    This simulates the scenario where a user changes dates in the UI.
    """
    room_service = RoomService()
    
    # Create cases for specific dates
    date1 = datetime(2025, 4, 22, timezone.utc)
    date2 = datetime(2025, 4, 23, timezone.utc)
    
    # Case only on date1
    case_date1 = mock_apella_case(
        date1.replace(hour=10),
        date1.replace(hour=12),
        CaseType.LIVE
    )
    
    cases_date1: List[ApellaCase] = [case_date1]
    cases_date2: List[ApellaCase] = []  # No cases on date2
    
    # Calculate status for date1 - should be IN_CASE
    calculation_time_date1 = date1.replace(hour=11)  # During the case
    status_date1 = await room_service.get_room_status(cases_date1, calculation_time_date1)
    
    assert status_date1.name == RoomStatusName.IN_CASE
    assert status_date1.computed_at == calculation_time_date1
    assert status_date1.in_progress_apella_case == case_date1
    
    # Calculate status for date2 - should be CLOSED (no cases)
    calculation_time_date2 = date2.replace(hour=11)  # Same time, different date
    status_date2 = await room_service.get_room_status(cases_date2, calculation_time_date2)
    
    assert status_date2.name == RoomStatusName.CLOSED
    assert status_date2.computed_at == calculation_time_date2
    assert status_date2.in_progress_apella_case is None


@pytest.mark.asyncio
async def test_room_status_calculation_timezone_handling() -> None:
    """
    Test that room status calculation handles timezones correctly.
    """
    room_service = RoomService()
    
    # Create a case in a specific timezone
    pst_tz = ZoneInfo("America/Los_Angeles")
    base_time_pst = datetime(2025, 4, 22, 10, 0, 0, pst_tz)
    
    case_start_pst = base_time_pst.replace(hour=9)
    case_end_pst = base_time_pst.replace(hour=11)
    live_case = mock_apella_case(case_start_pst, case_end_pst, CaseType.LIVE)
    
    cases: List[ApellaCase] = [live_case]
    
    # Calculate status using PST time
    calculation_time_pst = base_time_pst.replace(hour=10)
    status_pst = await room_service.get_room_status(cases, calculation_time_pst)
    
    assert status_pst.name == RoomStatusName.IN_CASE
    assert status_pst.computed_at == calculation_time_pst
    assert status_pst.since == case_start_pst
    
    # Convert to UTC and verify the calculation still works
    calculation_time_utc = calculation_time_pst.astimezone(timezone.utc)
    status_utc = await room_service.get_room_status(cases, calculation_time_utc)
    
    assert status_utc.name == RoomStatusName.IN_CASE
    assert status_utc.computed_at == calculation_time_utc


@pytest.mark.asyncio
async def test_room_status_calculation_edge_cases() -> None:
    """
    Test edge cases for room status calculation with specific dates.
    """
    room_service = RoomService()
    
    base_time = datetime(2025, 4, 22, 10, 0, 0, timezone.utc)
    
    # Case that starts and ends at the exact calculation time
    case_start = base_time
    case_end = base_time
    edge_case = mock_apella_case(case_start, case_end, CaseType.LIVE)
    
    cases: List[ApellaCase] = [edge_case]
    
    # Calculate status at the exact start/end time
    status = await room_service.get_room_status(cases, base_time)
    
    # Should be IN_CASE since the case is active at this exact moment
    assert status.name == RoomStatusName.IN_CASE
    assert status.computed_at == base_time
    
    # Calculate status one second after
    calculation_time_after = base_time + timedelta(seconds=1)
    status_after = await room_service.get_room_status(cases, calculation_time_after)
    
    # Should be IDLE since the case has ended
    assert status_after.name == RoomStatusName.IDLE
    assert status_after.computed_at == calculation_time_after


@pytest.mark.asyncio
async def test_room_status_calculation_with_turnover() -> None:
    """
    Test room status calculation with turnover scenarios using specific dates.
    """
    room_service = RoomService()
    
    base_time = datetime(2025, 4, 22, 12, 0, 0, timezone.utc)
    
    # Previous case that ended 30 minutes ago
    prev_case_end = base_time - timedelta(minutes=30)
    prev_case_start = prev_case_end - timedelta(hours=2)
    prev_case = mock_apella_case(prev_case_start, prev_case_end, CaseType.COMPLETE)
    
    # Next case that starts in 1 hour
    next_case_start = base_time + timedelta(hours=1)
    next_case_end = next_case_start + timedelta(hours=2)
    next_case = mock_apella_case(next_case_start, next_case_end, CaseType.FORECAST)
    
    cases: List[ApellaCase] = [prev_case, next_case]
    
    # Calculate status now - should be IDLE with turnover
    status = await room_service.get_room_status(cases, base_time, max_turnover_minutes=60)
    
    assert status.name == RoomStatusName.IDLE
    assert status.computed_at == base_time
    assert status.since == prev_case_end
    assert status.next_case == next_case
    assert status.in_progress_turnover is not None


@pytest.mark.asyncio
async def test_room_status_calculation_no_cases() -> None:
    """
    Test room status calculation when there are no cases for the specified date.
    """
    room_service = RoomService()
    
    # Empty case list
    cases: List[ApellaCase] = []
    
    # Calculate status for any date
    calculation_time = datetime(2025, 4, 22, 12, 0, 0, timezone.utc)
    status = await room_service.get_room_status(cases, calculation_time)
    
    # Should be CLOSED when no cases exist
    assert status.name == RoomStatusName.CLOSED
    assert status.computed_at == calculation_time
    assert status.in_progress_apella_case is None
    assert status.next_case is None
    assert status.in_progress_turnover is None
    
    # The 'since' time should be start of day for the calculation time
    expected_since = calculation_time.replace(hour=0, minute=0, second=0, microsecond=0)
    assert status.since == expected_since
