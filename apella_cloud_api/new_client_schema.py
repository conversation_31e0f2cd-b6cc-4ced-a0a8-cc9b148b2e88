import enum
from uuid import UUID  # noqa
from datetime import datetime, date, timedelta
from typing import Union
from .new_schema_generator.schema_generator_base_classes import GQLClientObject, Empty, NotFound


class GQLQuery(GQLClientObject):
    def __init__(
        self,
        me: "Union[GQLUser, NotFound, Empty]" = Empty(),
        user: "Union[GQLUser, NotFound, Empty]" = Empty(),
        users: "Union[GQLUserConnection, Empty]" = Empty(),
        organization: "Union[GQLOrganization, NotFound, Empty]" = Empty(),
        organizations: "Union[GQLOrganizationConnection, Empty]" = Empty(),
        site: "Union[GQLSite, NotFound, Empty]" = Empty(),
        sites: "Union[GQLSiteConnection, Empty]" = Empty(),
        room: "Union[GQLRoom, NotFound, Empty]" = Empty(),
        rooms: "Union[GQLRoomConnection, Empty]" = Empty(),
        room_closures: "Union[GQLRoomClosureConnection, Empty]" = Empty(),
        site_closures: "Union[GQLSiteClosureConnection, Empty]" = Empty(),
        room_tags: "Union[GQLRoomTagConnection, Empty]" = Empty(),
        camera: "Union[GQLCamera, NotFound, Empty]" = Empty(),
        cameras: "Union[GQLCameraConnection, Empty]" = Empty(),
        cluster: "Union[GQLCluster, NotFound, Empty]" = Empty(),
        clusters: "Union[GQLClusterConnection, NotFound, Empty]" = Empty(),
        event: "Union[GQLEvent, NotFound, Empty]" = Empty(),
        event_search: "Union[GQLEventConnection, Empty]" = Empty(),
        event_history_search: "Union[GQLEventConnection, Empty]" = Empty(),
        event_label_options: "Union[list[GQLEventLabelOption], Empty]" = Empty(),
        event_type: "Union[GQLEventType, NotFound, Empty]" = Empty(),
        event_types: "Union[GQLEventTypeConnection, NotFound, Empty]" = Empty(),
        highlight: "Union[GQLHighlight, NotFound, Empty]" = Empty(),
        highlights: "Union[GQLHighlightConnection, Empty]" = Empty(),
        highlight_search: "Union[GQLHighlightConnection, Empty]" = Empty(),
        highlight_feedback_search: "Union[GQLHighlightFeedbackConnection, Empty]" = Empty(),
        annotation_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
        annotation_tasks: "Union[GQLAnnotationTaskConnection, Empty]" = Empty(),
        annotation_task_type: "Union[GQLAnnotationTaskType, NotFound, Empty]" = Empty(),
        annotation_task_types: "Union[Union[list[GQLAnnotationTaskType], NotFound], Empty]" = Empty(),
        phases: "Union[GQLPhaseConnection, Empty]" = Empty(),
        apella_cases: "Union[GQLApellaCaseConnection, Empty]" = Empty(),
        phase_type: "Union[GQLPhaseTypeRecord, NotFound, Empty]" = Empty(),
        phase_types: "Union[GQLPhaseTypeConnection, Empty]" = Empty(),
        object_metrics: "Union[GQLObjectMetrics, NotFound, Empty]" = Empty(),
        scheduled_cases: "Union[GQLScheduledCaseConnection, Empty]" = Empty(),
        cases: "Union[GQLScheduledCaseConnection, Empty]" = Empty(),
        cases_history: "Union[GQLScheduledCaseConnection, Empty]" = Empty(),
        case_ehr_messages: "Union[GQLCaseEhrMessageConnection, Empty]" = Empty(),
        case_classification_types: "Union[Union[list[GQLCaseClassificationType], NotFound], Empty]" = Empty(),
        live_camera_images: "Union[GQLCameraLatestImageConnection, Empty]" = Empty(),
        service_lines: "Union[Union[list[GQLServiceLine], NotFound], Empty]" = Empty(),
        procedures: "Union[GQLProcedureConnection, Empty]" = Empty(),
        anesthesias: "Union[GQLAnesthesiaConnection, Empty]" = Empty(),
        measurement_period: "Union[GQLMeasurementPeriod, NotFound, Empty]" = Empty(),
        measurement_periods: "Union[GQLMeasurementPeriodConnection, Empty]" = Empty(),
        staff: "Union[GQLStaffConnection, Empty]" = Empty(),
        observation: "Union[GQLObservationConnection, Empty]" = Empty(),
        observation_type_names: "Union[list[Union[GQLObservationTypeName, Empty]], Empty]" = Empty(),
        observation_type_names_for_custom_phases: "Union[list[Union[GQLObservationTypeName, Empty]], Empty]" = Empty(),
        contact_information: "Union[GQLContactInformationConnection, Empty]" = Empty(),
        staff_event_notification_contact_information: "Union[GQLStaffEventNotificationContactInformationConnection, Empty]" = Empty(),
        staffing_needs_roles: "Union[list[GQLStaffRole], Empty]" = Empty(),
        block: "Union[GQLBlock, NotFound, Empty]" = Empty(),
        blocks: "Union[GQLBlockConnection, Empty]" = Empty(),
        block_time: "Union[GQLBlockTime, NotFound, Empty]" = Empty(),
        block_times_bulk: "Union[list[GQLBlockTime], Empty]" = Empty(),
        block_times: "Union[GQLBlockTimeConnection, Empty]" = Empty(),
        block_times_available_intervals: "Union[GQLBlockTimeAvailableIntervalConnection, Empty]" = Empty(),
        board_configs: "Union[GQLBoardConfigConnection, Empty]" = Empty(),
        case_duration_surgeons_and_procedures: "Union[GQLCaseDurationSurgeonProcedureMappingConnection, Empty]" = Empty(),
        case_duration_turnover_prediction: "Union[GQLCaseDurationTurnoverPrediction, NotFound, Empty]" = Empty(),
        case_duration_predictions: "Union[GQLCaseDurationPredictions, NotFound, Empty]" = Empty(),
        case_duration_surgeons: "Union[GQLCaseDurationSurgeonOptionConnection, Empty]" = Empty(),
        case_duration_procedures: "Union[GQLCaseDurationProcedureOptionConnection, Empty]" = Empty(),
        available_time_slots: "Union[Union[list[GQLAvailableTimeSlot], NotFound], Empty]" = Empty(),
        user_filter_views: "Union[Union[list[GQLUserFilterView], NotFound], Empty]" = Empty(),
        case_forecasts: "Union[GQLCaseForecastConnection, Empty]" = Empty(),
        dashboard_events: "Union[list[GQLEventDashboardVisibility], Empty]" = Empty(),
        turnover_labels: "Union[list[GQLTurnoverLabel], Empty]" = Empty(),
        cases_to_blocks: "Union[GQLCaseToBlockConnection, Empty]" = Empty(),
        case_to_block_overrides: "Union[Union[list[GQLCaseToBlockOverride], NotFound], Empty]" = Empty(),
        block_utilizations: "Union[Union[list[GQLBlockUtilization], NotFound], Empty]" = Empty(),
        custom_phase_configs: "Union[list[GQLCustomPhaseConfig], Empty]" = Empty(),
        terminal_clean_score: "Union[GQLTerminalCleanScore, NotFound, Empty]" = Empty(),
    ) -> None:
        self._me = me
        self._user = user
        self._users = users
        self._organization = organization
        self._organizations = organizations
        self._site = site
        self._sites = sites
        self._room = room
        self._rooms = rooms
        self._room_closures = room_closures
        self._site_closures = site_closures
        self._room_tags = room_tags
        self._camera = camera
        self._cameras = cameras
        self._cluster = cluster
        self._clusters = clusters
        self._event = event
        self._event_search = event_search
        self._event_history_search = event_history_search
        self._event_label_options = event_label_options
        self._event_type = event_type
        self._event_types = event_types
        self._highlight = highlight
        self._highlights = highlights
        self._highlight_search = highlight_search
        self._highlight_feedback_search = highlight_feedback_search
        self._annotation_task = annotation_task
        self._annotation_tasks = annotation_tasks
        self._annotation_task_type = annotation_task_type
        self._annotation_task_types = annotation_task_types
        self._phases = phases
        self._apella_cases = apella_cases
        self._phase_type = phase_type
        self._phase_types = phase_types
        self._object_metrics = object_metrics
        self._scheduled_cases = scheduled_cases
        self._cases = cases
        self._cases_history = cases_history
        self._case_ehr_messages = case_ehr_messages
        self._case_classification_types = case_classification_types
        self._live_camera_images = live_camera_images
        self._service_lines = service_lines
        self._procedures = procedures
        self._anesthesias = anesthesias
        self._measurement_period = measurement_period
        self._measurement_periods = measurement_periods
        self._staff = staff
        self._observation = observation
        self._observation_type_names = observation_type_names
        self._observation_type_names_for_custom_phases = observation_type_names_for_custom_phases
        self._contact_information = contact_information
        self._staff_event_notification_contact_information = (
            staff_event_notification_contact_information
        )
        self._staffing_needs_roles = staffing_needs_roles
        self._block = block
        self._blocks = blocks
        self._block_time = block_time
        self._block_times_bulk = block_times_bulk
        self._block_times = block_times
        self._block_times_available_intervals = block_times_available_intervals
        self._board_configs = board_configs
        self._case_duration_surgeons_and_procedures = case_duration_surgeons_and_procedures
        self._case_duration_turnover_prediction = case_duration_turnover_prediction
        self._case_duration_predictions = case_duration_predictions
        self._case_duration_surgeons = case_duration_surgeons
        self._case_duration_procedures = case_duration_procedures
        self._available_time_slots = available_time_slots
        self._user_filter_views = user_filter_views
        self._case_forecasts = case_forecasts
        self._dashboard_events = dashboard_events
        self._turnover_labels = turnover_labels
        self._cases_to_blocks = cases_to_blocks
        self._case_to_block_overrides = case_to_block_overrides
        self._block_utilizations = block_utilizations
        self._custom_phase_configs = custom_phase_configs
        self._terminal_clean_score = terminal_clean_score

    @property
    def me(self) -> "Union[GQLUser, NotFound]":
        if isinstance(self._me, Empty):
            raise ValueError("Field me was not selected as part of the query")
        return self._me

    @property
    def user(self) -> "Union[GQLUser, NotFound]":
        if isinstance(self._user, Empty):
            raise ValueError("Field user was not selected as part of the query")
        return self._user

    @property
    def users(self) -> "GQLUserConnection":
        if isinstance(self._users, Empty):
            raise ValueError("Field users was not selected as part of the query")
        return self._users

    @property
    def organization(self) -> "Union[GQLOrganization, NotFound]":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def organizations(self) -> "GQLOrganizationConnection":
        if isinstance(self._organizations, Empty):
            raise ValueError("Field organizations was not selected as part of the query")
        return self._organizations

    @property
    def site(self) -> "Union[GQLSite, NotFound]":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def sites(self) -> "GQLSiteConnection":
        if isinstance(self._sites, Empty):
            raise ValueError("Field sites was not selected as part of the query")
        return self._sites

    @property
    def room(self) -> "Union[GQLRoom, NotFound]":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def rooms(self) -> "GQLRoomConnection":
        if isinstance(self._rooms, Empty):
            raise ValueError("Field rooms was not selected as part of the query")
        return self._rooms

    @property
    def room_closures(self) -> "GQLRoomClosureConnection":
        if isinstance(self._room_closures, Empty):
            raise ValueError("Field room_closures was not selected as part of the query")
        return self._room_closures

    @property
    def site_closures(self) -> "GQLSiteClosureConnection":
        if isinstance(self._site_closures, Empty):
            raise ValueError("Field site_closures was not selected as part of the query")
        return self._site_closures

    @property
    def room_tags(self) -> "GQLRoomTagConnection":
        if isinstance(self._room_tags, Empty):
            raise ValueError("Field room_tags was not selected as part of the query")
        return self._room_tags

    @property
    def camera(self) -> "Union[GQLCamera, NotFound]":
        if isinstance(self._camera, Empty):
            raise ValueError("Field camera was not selected as part of the query")
        return self._camera

    @property
    def cameras(self) -> "GQLCameraConnection":
        if isinstance(self._cameras, Empty):
            raise ValueError("Field cameras was not selected as part of the query")
        return self._cameras

    @property
    def cluster(self) -> "Union[GQLCluster, NotFound]":
        if isinstance(self._cluster, Empty):
            raise ValueError("Field cluster was not selected as part of the query")
        return self._cluster

    @property
    def clusters(self) -> "Union[GQLClusterConnection, NotFound]":
        if isinstance(self._clusters, Empty):
            raise ValueError("Field clusters was not selected as part of the query")
        return self._clusters

    @property
    def event(self) -> "Union[GQLEvent, NotFound]":
        if isinstance(self._event, Empty):
            raise ValueError("Field event was not selected as part of the query")
        return self._event

    @property
    def event_search(self) -> "GQLEventConnection":
        if isinstance(self._event_search, Empty):
            raise ValueError("Field event_search was not selected as part of the query")
        return self._event_search

    @property
    def event_history_search(self) -> "GQLEventConnection":
        if isinstance(self._event_history_search, Empty):
            raise ValueError("Field event_history_search was not selected as part of the query")
        return self._event_history_search

    @property
    def event_label_options(self) -> "list[GQLEventLabelOption]":
        if isinstance(self._event_label_options, Empty):
            raise ValueError("Field event_label_options was not selected as part of the query")
        return self._event_label_options

    @property
    def event_type(self) -> "Union[GQLEventType, NotFound]":
        if isinstance(self._event_type, Empty):
            raise ValueError("Field event_type was not selected as part of the query")
        return self._event_type

    @property
    def event_types(self) -> "Union[GQLEventTypeConnection, NotFound]":
        if isinstance(self._event_types, Empty):
            raise ValueError("Field event_types was not selected as part of the query")
        return self._event_types

    @property
    def highlight(self) -> "Union[GQLHighlight, NotFound]":
        if isinstance(self._highlight, Empty):
            raise ValueError("Field highlight was not selected as part of the query")
        return self._highlight

    @property
    def highlights(self) -> "GQLHighlightConnection":
        if isinstance(self._highlights, Empty):
            raise ValueError("Field highlights was not selected as part of the query")
        return self._highlights

    @property
    def highlight_search(self) -> "GQLHighlightConnection":
        if isinstance(self._highlight_search, Empty):
            raise ValueError("Field highlight_search was not selected as part of the query")
        return self._highlight_search

    @property
    def highlight_feedback_search(self) -> "GQLHighlightFeedbackConnection":
        if isinstance(self._highlight_feedback_search, Empty):
            raise ValueError(
                "Field highlight_feedback_search was not selected as part of the query"
            )
        return self._highlight_feedback_search

    @property
    def annotation_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._annotation_task, Empty):
            raise ValueError("Field annotation_task was not selected as part of the query")
        return self._annotation_task

    @property
    def annotation_tasks(self) -> "GQLAnnotationTaskConnection":
        if isinstance(self._annotation_tasks, Empty):
            raise ValueError("Field annotation_tasks was not selected as part of the query")
        return self._annotation_tasks

    @property
    def annotation_task_type(self) -> "Union[GQLAnnotationTaskType, NotFound]":
        if isinstance(self._annotation_task_type, Empty):
            raise ValueError("Field annotation_task_type was not selected as part of the query")
        return self._annotation_task_type

    @property
    def annotation_task_types(self) -> "Union[Union[list[GQLAnnotationTaskType], NotFound]]":
        if isinstance(self._annotation_task_types, Empty):
            raise ValueError("Field annotation_task_types was not selected as part of the query")
        return self._annotation_task_types

    @property
    def phases(self) -> "GQLPhaseConnection":
        if isinstance(self._phases, Empty):
            raise ValueError("Field phases was not selected as part of the query")
        return self._phases

    @property
    def apella_cases(self) -> "GQLApellaCaseConnection":
        if isinstance(self._apella_cases, Empty):
            raise ValueError("Field apella_cases was not selected as part of the query")
        return self._apella_cases

    @property
    def phase_type(self) -> "Union[GQLPhaseTypeRecord, NotFound]":
        if isinstance(self._phase_type, Empty):
            raise ValueError("Field phase_type was not selected as part of the query")
        return self._phase_type

    @property
    def phase_types(self) -> "GQLPhaseTypeConnection":
        if isinstance(self._phase_types, Empty):
            raise ValueError("Field phase_types was not selected as part of the query")
        return self._phase_types

    @property
    def object_metrics(self) -> "Union[GQLObjectMetrics, NotFound]":
        if isinstance(self._object_metrics, Empty):
            raise ValueError("Field object_metrics was not selected as part of the query")
        return self._object_metrics

    @property
    def scheduled_cases(self) -> "GQLScheduledCaseConnection":
        if isinstance(self._scheduled_cases, Empty):
            raise ValueError("Field scheduled_cases was not selected as part of the query")
        return self._scheduled_cases

    @property
    def cases(self) -> "GQLScheduledCaseConnection":
        if isinstance(self._cases, Empty):
            raise ValueError("Field cases was not selected as part of the query")
        return self._cases

    @property
    def cases_history(self) -> "GQLScheduledCaseConnection":
        if isinstance(self._cases_history, Empty):
            raise ValueError("Field cases_history was not selected as part of the query")
        return self._cases_history

    @property
    def case_ehr_messages(self) -> "GQLCaseEhrMessageConnection":
        if isinstance(self._case_ehr_messages, Empty):
            raise ValueError("Field case_ehr_messages was not selected as part of the query")
        return self._case_ehr_messages

    @property
    def case_classification_types(
        self,
    ) -> "Union[Union[list[GQLCaseClassificationType], NotFound]]":
        if isinstance(self._case_classification_types, Empty):
            raise ValueError(
                "Field case_classification_types was not selected as part of the query"
            )
        return self._case_classification_types

    @property
    def live_camera_images(self) -> "GQLCameraLatestImageConnection":
        if isinstance(self._live_camera_images, Empty):
            raise ValueError("Field live_camera_images was not selected as part of the query")
        return self._live_camera_images

    @property
    def service_lines(self) -> "Union[Union[list[GQLServiceLine], NotFound]]":
        if isinstance(self._service_lines, Empty):
            raise ValueError("Field service_lines was not selected as part of the query")
        return self._service_lines

    @property
    def procedures(self) -> "GQLProcedureConnection":
        if isinstance(self._procedures, Empty):
            raise ValueError("Field procedures was not selected as part of the query")
        return self._procedures

    @property
    def anesthesias(self) -> "GQLAnesthesiaConnection":
        if isinstance(self._anesthesias, Empty):
            raise ValueError("Field anesthesias was not selected as part of the query")
        return self._anesthesias

    @property
    def measurement_period(self) -> "Union[GQLMeasurementPeriod, NotFound]":
        if isinstance(self._measurement_period, Empty):
            raise ValueError("Field measurement_period was not selected as part of the query")
        return self._measurement_period

    @property
    def measurement_periods(self) -> "GQLMeasurementPeriodConnection":
        if isinstance(self._measurement_periods, Empty):
            raise ValueError("Field measurement_periods was not selected as part of the query")
        return self._measurement_periods

    @property
    def staff(self) -> "GQLStaffConnection":
        if isinstance(self._staff, Empty):
            raise ValueError("Field staff was not selected as part of the query")
        return self._staff

    @property
    def observation(self) -> "GQLObservationConnection":
        if isinstance(self._observation, Empty):
            raise ValueError("Field observation was not selected as part of the query")
        return self._observation

    @property
    def observation_type_names(self) -> "Union[list[Union[GQLObservationTypeName, Empty]]]":
        if isinstance(self._observation_type_names, Empty):
            raise ValueError("Field observation_type_names was not selected as part of the query")
        return self._observation_type_names

    @property
    def observation_type_names_for_custom_phases(
        self,
    ) -> "Union[list[Union[GQLObservationTypeName, Empty]]]":
        if isinstance(self._observation_type_names_for_custom_phases, Empty):
            raise ValueError(
                "Field observation_type_names_for_custom_phases was not selected as part of the query"
            )
        return self._observation_type_names_for_custom_phases

    @property
    def contact_information(self) -> "GQLContactInformationConnection":
        if isinstance(self._contact_information, Empty):
            raise ValueError("Field contact_information was not selected as part of the query")
        return self._contact_information

    @property
    def staff_event_notification_contact_information(
        self,
    ) -> "GQLStaffEventNotificationContactInformationConnection":
        if isinstance(self._staff_event_notification_contact_information, Empty):
            raise ValueError(
                "Field staff_event_notification_contact_information was not selected as part of the query"
            )
        return self._staff_event_notification_contact_information

    @property
    def staffing_needs_roles(self) -> "list[GQLStaffRole]":
        if isinstance(self._staffing_needs_roles, Empty):
            raise ValueError("Field staffing_needs_roles was not selected as part of the query")
        return self._staffing_needs_roles

    @property
    def block(self) -> "Union[GQLBlock, NotFound]":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block

    @property
    def blocks(self) -> "GQLBlockConnection":
        if isinstance(self._blocks, Empty):
            raise ValueError("Field blocks was not selected as part of the query")
        return self._blocks

    @property
    def block_time(self) -> "Union[GQLBlockTime, NotFound]":
        if isinstance(self._block_time, Empty):
            raise ValueError("Field block_time was not selected as part of the query")
        return self._block_time

    @property
    def block_times_bulk(self) -> "list[GQLBlockTime]":
        if isinstance(self._block_times_bulk, Empty):
            raise ValueError("Field block_times_bulk was not selected as part of the query")
        return self._block_times_bulk

    @property
    def block_times(self) -> "GQLBlockTimeConnection":
        if isinstance(self._block_times, Empty):
            raise ValueError("Field block_times was not selected as part of the query")
        return self._block_times

    @property
    def block_times_available_intervals(self) -> "GQLBlockTimeAvailableIntervalConnection":
        if isinstance(self._block_times_available_intervals, Empty):
            raise ValueError(
                "Field block_times_available_intervals was not selected as part of the query"
            )
        return self._block_times_available_intervals

    @property
    def board_configs(self) -> "GQLBoardConfigConnection":
        if isinstance(self._board_configs, Empty):
            raise ValueError("Field board_configs was not selected as part of the query")
        return self._board_configs

    @property
    def case_duration_surgeons_and_procedures(
        self,
    ) -> "GQLCaseDurationSurgeonProcedureMappingConnection":
        if isinstance(self._case_duration_surgeons_and_procedures, Empty):
            raise ValueError(
                "Field case_duration_surgeons_and_procedures was not selected as part of the query"
            )
        return self._case_duration_surgeons_and_procedures

    @property
    def case_duration_turnover_prediction(
        self,
    ) -> "Union[GQLCaseDurationTurnoverPrediction, NotFound]":
        if isinstance(self._case_duration_turnover_prediction, Empty):
            raise ValueError(
                "Field case_duration_turnover_prediction was not selected as part of the query"
            )
        return self._case_duration_turnover_prediction

    @property
    def case_duration_predictions(self) -> "Union[GQLCaseDurationPredictions, NotFound]":
        if isinstance(self._case_duration_predictions, Empty):
            raise ValueError(
                "Field case_duration_predictions was not selected as part of the query"
            )
        return self._case_duration_predictions

    @property
    def case_duration_surgeons(self) -> "GQLCaseDurationSurgeonOptionConnection":
        if isinstance(self._case_duration_surgeons, Empty):
            raise ValueError("Field case_duration_surgeons was not selected as part of the query")
        return self._case_duration_surgeons

    @property
    def case_duration_procedures(self) -> "GQLCaseDurationProcedureOptionConnection":
        if isinstance(self._case_duration_procedures, Empty):
            raise ValueError("Field case_duration_procedures was not selected as part of the query")
        return self._case_duration_procedures

    @property
    def available_time_slots(self) -> "Union[Union[list[GQLAvailableTimeSlot], NotFound]]":
        if isinstance(self._available_time_slots, Empty):
            raise ValueError("Field available_time_slots was not selected as part of the query")
        return self._available_time_slots

    @property
    def user_filter_views(self) -> "Union[Union[list[GQLUserFilterView], NotFound]]":
        if isinstance(self._user_filter_views, Empty):
            raise ValueError("Field user_filter_views was not selected as part of the query")
        return self._user_filter_views

    @property
    def case_forecasts(self) -> "GQLCaseForecastConnection":
        if isinstance(self._case_forecasts, Empty):
            raise ValueError("Field case_forecasts was not selected as part of the query")
        return self._case_forecasts

    @property
    def dashboard_events(self) -> "list[GQLEventDashboardVisibility]":
        if isinstance(self._dashboard_events, Empty):
            raise ValueError("Field dashboard_events was not selected as part of the query")
        return self._dashboard_events

    @property
    def turnover_labels(self) -> "list[GQLTurnoverLabel]":
        if isinstance(self._turnover_labels, Empty):
            raise ValueError("Field turnover_labels was not selected as part of the query")
        return self._turnover_labels

    @property
    def cases_to_blocks(self) -> "GQLCaseToBlockConnection":
        if isinstance(self._cases_to_blocks, Empty):
            raise ValueError("Field cases_to_blocks was not selected as part of the query")
        return self._cases_to_blocks

    @property
    def case_to_block_overrides(self) -> "Union[Union[list[GQLCaseToBlockOverride], NotFound]]":
        if isinstance(self._case_to_block_overrides, Empty):
            raise ValueError("Field case_to_block_overrides was not selected as part of the query")
        return self._case_to_block_overrides

    @property
    def block_utilizations(self) -> "Union[Union[list[GQLBlockUtilization], NotFound]]":
        if isinstance(self._block_utilizations, Empty):
            raise ValueError("Field block_utilizations was not selected as part of the query")
        return self._block_utilizations

    @property
    def custom_phase_configs(self) -> "list[GQLCustomPhaseConfig]":
        if isinstance(self._custom_phase_configs, Empty):
            raise ValueError("Field custom_phase_configs was not selected as part of the query")
        return self._custom_phase_configs

    @property
    def terminal_clean_score(self) -> "Union[GQLTerminalCleanScore, NotFound]":
        if isinstance(self._terminal_clean_score, Empty):
            raise ValueError("Field terminal_clean_score was not selected as part of the query")
        return self._terminal_clean_score


class GQLUser(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        email: "Union[str, Empty]" = Empty(),
        organizations: "Union[GQLOrganizationConnection, Empty]" = Empty(),
        ui_permissions: "Union[GQLUserUiPermissions, NotFound, Empty]" = Empty(),
        permissions: "Union[list[Union[str, Empty, None]], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._email = email
        self._organizations = organizations
        self._ui_permissions = ui_permissions
        self._permissions = permissions

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def email(self) -> "str":
        if isinstance(self._email, Empty):
            raise ValueError("Field email was not selected as part of the query")
        return self._email

    @property
    def organizations(self) -> "GQLOrganizationConnection":
        if isinstance(self._organizations, Empty):
            raise ValueError("Field organizations was not selected as part of the query")
        return self._organizations

    @property
    def ui_permissions(self) -> "Union[GQLUserUiPermissions, NotFound]":
        if isinstance(self._ui_permissions, Empty):
            raise ValueError("Field ui_permissions was not selected as part of the query")
        return self._ui_permissions

    @property
    def permissions(self) -> "Union[list[Union[str, Empty, None]]]":
        if isinstance(self._permissions, Empty):
            raise ValueError("Field permissions was not selected as part of the query")
        return self._permissions


class GQLOrganizationConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLOrganizationEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLOrganizationEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLPageInfo(GQLClientObject):
    def __init__(
        self,
        has_next_page: "Union[bool, Empty]" = Empty(),
        has_previous_page: "Union[bool, Empty]" = Empty(),
        start_cursor: "Union[str, NotFound, Empty]" = Empty(),
        end_cursor: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._has_next_page = has_next_page
        self._has_previous_page = has_previous_page
        self._start_cursor = start_cursor
        self._end_cursor = end_cursor

    @property
    def has_next_page(self) -> "bool":
        if isinstance(self._has_next_page, Empty):
            raise ValueError("Field has_next_page was not selected as part of the query")
        return self._has_next_page

    @property
    def has_previous_page(self) -> "bool":
        if isinstance(self._has_previous_page, Empty):
            raise ValueError("Field has_previous_page was not selected as part of the query")
        return self._has_previous_page

    @property
    def start_cursor(self) -> "Union[str, NotFound]":
        if isinstance(self._start_cursor, Empty):
            raise ValueError("Field start_cursor was not selected as part of the query")
        return self._start_cursor

    @property
    def end_cursor(self) -> "Union[str, NotFound]":
        if isinstance(self._end_cursor, Empty):
            raise ValueError("Field end_cursor was not selected as part of the query")
        return self._end_cursor


class GQLOrganizationEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLOrganization, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLOrganization":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLOrganization(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        auth0_org_id: "Union[str, Empty]" = Empty(),
        sites: "Union[GQLSiteConnection, Empty]" = Empty(),
        users: "Union[GQLUserConnection, Empty]" = Empty(),
        event_types: "Union[list[str], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._auth0_org_id = auth0_org_id
        self._sites = sites
        self._users = users
        self._event_types = event_types

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def auth0_org_id(self) -> "str":
        if isinstance(self._auth0_org_id, Empty):
            raise ValueError("Field auth0_org_id was not selected as part of the query")
        return self._auth0_org_id

    @property
    def sites(self) -> "GQLSiteConnection":
        if isinstance(self._sites, Empty):
            raise ValueError("Field sites was not selected as part of the query")
        return self._sites

    @property
    def users(self) -> "GQLUserConnection":
        if isinstance(self._users, Empty):
            raise ValueError("Field users was not selected as part of the query")
        return self._users

    @property
    def event_types(self) -> "list[str]":
        if isinstance(self._event_types, Empty):
            raise ValueError("Field event_types was not selected as part of the query")
        return self._event_types


class GQLSiteConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLSiteEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLSiteEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLSiteEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLSite, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLSite":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLSite(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        timezone: "Union[str, Empty]" = Empty(),
        organization_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        rooms: "Union[GQLRoomConnection, Empty]" = Empty(),
        turnover_goals: "Union[GQLTurnoverGoals, Empty]" = Empty(),
        prime_time_config: "Union[GQLSitePrimeTimeConfig, Empty]" = Empty(),
        first_case_config: "Union[GQLSiteFirstCaseConfig, Empty]" = Empty(),
        capacity_constraints: "Union[list[GQLSiteCapacityConstraint], Empty]" = Empty(),
        staffing_needs_ratios: "Union[list[GQLStaffingNeedsRatio], Empty]" = Empty(),
        closures: "Union[GQLSiteClosureConnection, NotFound, Empty]" = Empty(),
        case_label_form: "Union[list[GQLCaseLabelCategory], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._timezone = timezone
        self._organization_id = organization_id
        self._organization = organization
        self._rooms = rooms
        self._turnover_goals = turnover_goals
        self._prime_time_config = prime_time_config
        self._first_case_config = first_case_config
        self._capacity_constraints = capacity_constraints
        self._staffing_needs_ratios = staffing_needs_ratios
        self._closures = closures
        self._case_label_form = case_label_form

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def timezone(self) -> "str":
        if isinstance(self._timezone, Empty):
            raise ValueError("Field timezone was not selected as part of the query")
        return self._timezone

    @property
    def organization_id(self) -> "str":
        if isinstance(self._organization_id, Empty):
            raise ValueError("Field organization_id was not selected as part of the query")
        return self._organization_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def rooms(self) -> "GQLRoomConnection":
        if isinstance(self._rooms, Empty):
            raise ValueError("Field rooms was not selected as part of the query")
        return self._rooms

    @property
    def turnover_goals(self) -> "GQLTurnoverGoals":
        if isinstance(self._turnover_goals, Empty):
            raise ValueError("Field turnover_goals was not selected as part of the query")
        return self._turnover_goals

    @property
    def prime_time_config(self) -> "GQLSitePrimeTimeConfig":
        if isinstance(self._prime_time_config, Empty):
            raise ValueError("Field prime_time_config was not selected as part of the query")
        return self._prime_time_config

    @property
    def first_case_config(self) -> "GQLSiteFirstCaseConfig":
        if isinstance(self._first_case_config, Empty):
            raise ValueError("Field first_case_config was not selected as part of the query")
        return self._first_case_config

    @property
    def capacity_constraints(self) -> "list[GQLSiteCapacityConstraint]":
        if isinstance(self._capacity_constraints, Empty):
            raise ValueError("Field capacity_constraints was not selected as part of the query")
        return self._capacity_constraints

    @property
    def staffing_needs_ratios(self) -> "list[GQLStaffingNeedsRatio]":
        if isinstance(self._staffing_needs_ratios, Empty):
            raise ValueError("Field staffing_needs_ratios was not selected as part of the query")
        return self._staffing_needs_ratios

    @property
    def closures(self) -> "Union[GQLSiteClosureConnection, NotFound]":
        if isinstance(self._closures, Empty):
            raise ValueError("Field closures was not selected as part of the query")
        return self._closures

    @property
    def case_label_form(self) -> "list[GQLCaseLabelCategory]":
        if isinstance(self._case_label_form, Empty):
            raise ValueError("Field case_label_form was not selected as part of the query")
        return self._case_label_form


class GQLRoomConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLRoomEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLRoomEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLRoomEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLRoom, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLRoom":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLRoom(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        organization_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        cameras: "Union[GQLCameraConnection, Empty]" = Empty(),
        default_camera: "Union[GQLCamera, NotFound, Empty]" = Empty(),
        status: "Union[GQLRoomStatus, Empty]" = Empty(),
        sort_key: "Union[str, NotFound, Empty]" = Empty(),
        has_playlist_available: "Union[bool, Empty]" = Empty(),
        events: "Union[list[GQLEvent], Empty]" = Empty(),
        turnovers: "Union[list[GQLTurnover], Empty]" = Empty(),
        apella_cases: "Union[GQLApellaCaseConnection, Empty]" = Empty(),
        room_events: "Union[GQLEventConnection, Empty]" = Empty(),
        block_times: "Union[GQLBlockTimeConnection, Empty]" = Empty(),
        privacy_enabled: "Union[bool, Empty]" = Empty(),
        is_forecasting_enabled: "Union[bool, Empty]" = Empty(),
        tags: "Union[list[GQLRoomTag], Empty]" = Empty(),
        closures: "Union[GQLRoomClosureConnection, Empty]" = Empty(),
        prime_time_config: "Union[GQLRoomPrimeTimeConfig, Empty]" = Empty(),
        first_case_config: "Union[GQLRoomFirstCaseConfig, Empty]" = Empty(),
        labels: "Union[dict[str, str], NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._organization_id = organization_id
        self._organization = organization
        self._site_id = site_id
        self._site = site
        self._cameras = cameras
        self._default_camera = default_camera
        self._status = status
        self._sort_key = sort_key
        self._has_playlist_available = has_playlist_available
        self._events = events
        self._turnovers = turnovers
        self._apella_cases = apella_cases
        self._room_events = room_events
        self._block_times = block_times
        self._privacy_enabled = privacy_enabled
        self._is_forecasting_enabled = is_forecasting_enabled
        self._tags = tags
        self._closures = closures
        self._prime_time_config = prime_time_config
        self._first_case_config = first_case_config
        self._labels = labels

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def organization_id(self) -> "str":
        if isinstance(self._organization_id, Empty):
            raise ValueError("Field organization_id was not selected as part of the query")
        return self._organization_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def cameras(self) -> "GQLCameraConnection":
        if isinstance(self._cameras, Empty):
            raise ValueError("Field cameras was not selected as part of the query")
        return self._cameras

    @property
    def default_camera(self) -> "Union[GQLCamera, NotFound]":
        if isinstance(self._default_camera, Empty):
            raise ValueError("Field default_camera was not selected as part of the query")
        return self._default_camera

    @property
    def status(self) -> "GQLRoomStatus":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def sort_key(self) -> "Union[str, NotFound]":
        if isinstance(self._sort_key, Empty):
            raise ValueError("Field sort_key was not selected as part of the query")
        return self._sort_key

    @property
    def has_playlist_available(self) -> "bool":
        if isinstance(self._has_playlist_available, Empty):
            raise ValueError("Field has_playlist_available was not selected as part of the query")
        return self._has_playlist_available

    @property
    def events(self) -> "list[GQLEvent]":
        if isinstance(self._events, Empty):
            raise ValueError("Field events was not selected as part of the query")
        return self._events

    @property
    def turnovers(self) -> "list[GQLTurnover]":
        if isinstance(self._turnovers, Empty):
            raise ValueError("Field turnovers was not selected as part of the query")
        return self._turnovers

    @property
    def apella_cases(self) -> "GQLApellaCaseConnection":
        if isinstance(self._apella_cases, Empty):
            raise ValueError("Field apella_cases was not selected as part of the query")
        return self._apella_cases

    @property
    def room_events(self) -> "GQLEventConnection":
        if isinstance(self._room_events, Empty):
            raise ValueError("Field room_events was not selected as part of the query")
        return self._room_events

    @property
    def block_times(self) -> "GQLBlockTimeConnection":
        if isinstance(self._block_times, Empty):
            raise ValueError("Field block_times was not selected as part of the query")
        return self._block_times

    @property
    def privacy_enabled(self) -> "bool":
        if isinstance(self._privacy_enabled, Empty):
            raise ValueError("Field privacy_enabled was not selected as part of the query")
        return self._privacy_enabled

    @property
    def is_forecasting_enabled(self) -> "bool":
        if isinstance(self._is_forecasting_enabled, Empty):
            raise ValueError("Field is_forecasting_enabled was not selected as part of the query")
        return self._is_forecasting_enabled

    @property
    def tags(self) -> "list[GQLRoomTag]":
        if isinstance(self._tags, Empty):
            raise ValueError("Field tags was not selected as part of the query")
        return self._tags

    @property
    def closures(self) -> "GQLRoomClosureConnection":
        if isinstance(self._closures, Empty):
            raise ValueError("Field closures was not selected as part of the query")
        return self._closures

    @property
    def prime_time_config(self) -> "GQLRoomPrimeTimeConfig":
        if isinstance(self._prime_time_config, Empty):
            raise ValueError("Field prime_time_config was not selected as part of the query")
        return self._prime_time_config

    @property
    def first_case_config(self) -> "GQLRoomFirstCaseConfig":
        if isinstance(self._first_case_config, Empty):
            raise ValueError("Field first_case_config was not selected as part of the query")
        return self._first_case_config

    @property
    def labels(self) -> "Union[dict[str, str], NotFound]":
        if isinstance(self._labels, Empty):
            raise ValueError("Field labels was not selected as part of the query")
        return self._labels


class GQLCameraConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCameraEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCameraEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCameraEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCamera, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCamera":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCamera(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        organization_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        latest_image: "Union[GQLCameraLatestImage, NotFound, Empty]" = Empty(),
        labels: "Union[dict[str, str], NotFound, Empty]" = Empty(),
        rtsp_url: "Union[str, NotFound, Empty]" = Empty(),
        family: "Union[str, Empty]" = Empty(),
        patient_bounding_box: "Union[GQLPatientBoundingBox, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._organization_id = organization_id
        self._organization = organization
        self._site_id = site_id
        self._site = site
        self._room_id = room_id
        self._room = room
        self._latest_image = latest_image
        self._labels = labels
        self._rtsp_url = rtsp_url
        self._family = family
        self._patient_bounding_box = patient_bounding_box

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def organization_id(self) -> "str":
        if isinstance(self._organization_id, Empty):
            raise ValueError("Field organization_id was not selected as part of the query")
        return self._organization_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def latest_image(self) -> "Union[GQLCameraLatestImage, NotFound]":
        if isinstance(self._latest_image, Empty):
            raise ValueError("Field latest_image was not selected as part of the query")
        return self._latest_image

    @property
    def labels(self) -> "Union[dict[str, str], NotFound]":
        if isinstance(self._labels, Empty):
            raise ValueError("Field labels was not selected as part of the query")
        return self._labels

    @property
    def rtsp_url(self) -> "Union[str, NotFound]":
        if isinstance(self._rtsp_url, Empty):
            raise ValueError("Field rtsp_url was not selected as part of the query")
        return self._rtsp_url

    @property
    def family(self) -> "str":
        if isinstance(self._family, Empty):
            raise ValueError("Field family was not selected as part of the query")
        return self._family

    @property
    def patient_bounding_box(self) -> "Union[GQLPatientBoundingBox, NotFound]":
        if isinstance(self._patient_bounding_box, Empty):
            raise ValueError("Field patient_bounding_box was not selected as part of the query")
        return self._patient_bounding_box


class GQLCameraLatestImage(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        image_uri: "Union[str, Empty]" = Empty(),
        capture_time: "Union[datetime, Empty]" = Empty(),
        image_bytes: "Union[str, NotFound, Empty]" = Empty(),
        camera: "Union[GQLCamera, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._image_uri = image_uri
        self._capture_time = capture_time
        self._image_bytes = image_bytes
        self._camera = camera

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def image_uri(self) -> "str":
        if isinstance(self._image_uri, Empty):
            raise ValueError("Field image_uri was not selected as part of the query")
        return self._image_uri

    @property
    def capture_time(self) -> "datetime":
        if isinstance(self._capture_time, Empty):
            raise ValueError("Field capture_time was not selected as part of the query")
        return self._capture_time

    @property
    def image_bytes(self) -> "Union[str, NotFound]":
        if isinstance(self._image_bytes, Empty):
            raise ValueError("Field image_bytes was not selected as part of the query")
        return self._image_bytes

    @property
    def camera(self) -> "GQLCamera":
        if isinstance(self._camera, Empty):
            raise ValueError("Field camera was not selected as part of the query")
        return self._camera


class GQLPatientBoundingBox(GQLClientObject):
    def __init__(
        self,
        camera_id: "Union[str, Empty]" = Empty(),
        left_pct: "Union[float, Empty]" = Empty(),
        bottom_pct: "Union[float, Empty]" = Empty(),
        width_pct: "Union[float, Empty]" = Empty(),
        height_pct: "Union[float, Empty]" = Empty(),
    ) -> None:
        self._camera_id = camera_id
        self._left_pct = left_pct
        self._bottom_pct = bottom_pct
        self._width_pct = width_pct
        self._height_pct = height_pct

    @property
    def camera_id(self) -> "str":
        if isinstance(self._camera_id, Empty):
            raise ValueError("Field camera_id was not selected as part of the query")
        return self._camera_id

    @property
    def left_pct(self) -> "float":
        if isinstance(self._left_pct, Empty):
            raise ValueError("Field left_pct was not selected as part of the query")
        return self._left_pct

    @property
    def bottom_pct(self) -> "float":
        if isinstance(self._bottom_pct, Empty):
            raise ValueError("Field bottom_pct was not selected as part of the query")
        return self._bottom_pct

    @property
    def width_pct(self) -> "float":
        if isinstance(self._width_pct, Empty):
            raise ValueError("Field width_pct was not selected as part of the query")
        return self._width_pct

    @property
    def height_pct(self) -> "float":
        if isinstance(self._height_pct, Empty):
            raise ValueError("Field height_pct was not selected as part of the query")
        return self._height_pct


class GQLPageCursors(GQLClientObject):
    def __init__(
        self,
        around: "Union[Union[list[GQLPageCursor], NotFound], Empty]" = Empty(),
        first: "Union[GQLPageCursor, NotFound, Empty]" = Empty(),
        last: "Union[GQLPageCursor, NotFound, Empty]" = Empty(),
        next: "Union[GQLPageCursor, NotFound, Empty]" = Empty(),
        previous: "Union[GQLPageCursor, NotFound, Empty]" = Empty(),
    ) -> None:
        self._around = around
        self._first = first
        self._last = last
        self._next = next
        self._previous = previous

    @property
    def around(self) -> "Union[Union[list[GQLPageCursor], NotFound]]":
        if isinstance(self._around, Empty):
            raise ValueError("Field around was not selected as part of the query")
        return self._around

    @property
    def first(self) -> "Union[GQLPageCursor, NotFound]":
        if isinstance(self._first, Empty):
            raise ValueError("Field first was not selected as part of the query")
        return self._first

    @property
    def last(self) -> "Union[GQLPageCursor, NotFound]":
        if isinstance(self._last, Empty):
            raise ValueError("Field last was not selected as part of the query")
        return self._last

    @property
    def next(self) -> "Union[GQLPageCursor, NotFound]":
        if isinstance(self._next, Empty):
            raise ValueError("Field next was not selected as part of the query")
        return self._next

    @property
    def previous(self) -> "Union[GQLPageCursor, NotFound]":
        if isinstance(self._previous, Empty):
            raise ValueError("Field previous was not selected as part of the query")
        return self._previous


class GQLPageCursor(GQLClientObject):
    def __init__(
        self,
        cursor: "Union[str, Empty]" = Empty(),
        is_current: "Union[bool, Empty]" = Empty(),
        page: "Union[int, Empty]" = Empty(),
    ) -> None:
        self._cursor = cursor
        self._is_current = is_current
        self._page = page

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor

    @property
    def is_current(self) -> "bool":
        if isinstance(self._is_current, Empty):
            raise ValueError("Field is_current was not selected as part of the query")
        return self._is_current

    @property
    def page(self) -> "int":
        if isinstance(self._page, Empty):
            raise ValueError("Field page was not selected as part of the query")
        return self._page


class GQLRoomStatus(GQLClientObject):
    def __init__(
        self,
        name: "Union[GQLRoomStatusName, Empty]" = Empty(),
        since: "Union[datetime, Empty]" = Empty(),
        calculated_at: "Union[datetime, Empty]" = Empty(),
        in_progress_apella_case: "Union[GQLApellaCase, NotFound, Empty]" = Empty(),
        in_progress_turnover: "Union[GQLTurnover, NotFound, Empty]" = Empty(),
        next_case: "Union[GQLApellaCase, NotFound, Empty]" = Empty(),
    ) -> None:
        self._name = name
        self._since = since
        self._calculated_at = calculated_at
        self._in_progress_apella_case = in_progress_apella_case
        self._in_progress_turnover = in_progress_turnover
        self._next_case = next_case

    @property
    def name(self) -> "GQLRoomStatusName":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def since(self) -> "datetime":
        if isinstance(self._since, Empty):
            raise ValueError("Field since was not selected as part of the query")
        return self._since

    @property
    def calculated_at(self) -> "datetime":
        if isinstance(self._calculated_at, Empty):
            raise ValueError("Field calculated_at was not selected as part of the query")
        return self._calculated_at

    @property
    def in_progress_apella_case(self) -> "Union[GQLApellaCase, NotFound]":
        if isinstance(self._in_progress_apella_case, Empty):
            raise ValueError("Field in_progress_apella_case was not selected as part of the query")
        return self._in_progress_apella_case

    @property
    def in_progress_turnover(self) -> "Union[GQLTurnover, NotFound]":
        if isinstance(self._in_progress_turnover, Empty):
            raise ValueError("Field in_progress_turnover was not selected as part of the query")
        return self._in_progress_turnover

    @property
    def next_case(self) -> "Union[GQLApellaCase, NotFound]":
        if isinstance(self._next_case, Empty):
            raise ValueError("Field next_case was not selected as part of the query")
        return self._next_case


class GQLApellaCase(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        actual: "Union[GQLPhase, NotFound, Empty]" = Empty(),
        forecast: "Union[GQLPhase, NotFound, Empty]" = Empty(),
        case_forecast: "Union[GQLCaseForecast, NotFound, Empty]" = Empty(),
        type: "Union[GQLCaseType, Empty]" = Empty(),
        status: "Union[GQLApellaCaseStatus, Empty]" = Empty(),
        source: "Union[GQLCaseSource, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._case = case
        self._actual = actual
        self._forecast = forecast
        self._case_forecast = case_forecast
        self._type = type
        self._status = status
        self._source = source
        self._start_time = start_time
        self._end_time = end_time
        self._room = room
        self._room_id = room_id
        self._site_id = site_id

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def actual(self) -> "Union[GQLPhase, NotFound]":
        if isinstance(self._actual, Empty):
            raise ValueError("Field actual was not selected as part of the query")
        return self._actual

    @property
    def forecast(self) -> "Union[GQLPhase, NotFound]":
        if isinstance(self._forecast, Empty):
            raise ValueError("Field forecast was not selected as part of the query")
        return self._forecast

    @property
    def case_forecast(self) -> "Union[GQLCaseForecast, NotFound]":
        if isinstance(self._case_forecast, Empty):
            raise ValueError("Field case_forecast was not selected as part of the query")
        return self._case_forecast

    @property
    def type(self) -> "GQLCaseType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def status(self) -> "GQLApellaCaseStatus":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def source(self) -> "GQLCaseSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id


class GQLScheduledCase(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        scheduled_start_time: "Union[datetime, Empty]" = Empty(),
        scheduled_end_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        case_classification_type: "Union[GQLCaseClassificationType, NotFound, Empty]" = Empty(),
        status: "Union[str, Empty]" = Empty(),
        case_staff: "Union[list[GQLCaseStaff], Empty]" = Empty(),
        case_staff_plan: "Union[GQLCaseStaffPlanConnection, Empty]" = Empty(),
        note_plan: "Union[GQLCaseNotePlan, NotFound, Empty]" = Empty(),
        staff: "Union[GQLStaffConnection, Empty]" = Empty(),
        is_in_flip_room: "Union[bool, NotFound, Empty]" = Empty(),
        preceding_case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        is_first_case: "Union[bool, NotFound, Empty]" = Empty(),
        case_procedures: "Union[list[GQLCaseProcedure], Empty]" = Empty(),
        procedures: "Union[GQLProcedureConnection, Empty]" = Empty(),
        is_add_on: "Union[bool, NotFound, Empty]" = Empty(),
        patient: "Union[GQLPatient, NotFound, Empty]" = Empty(),
        patient_class: "Union[GQLPatientClass, NotFound, Empty]" = Empty(),
        version: "Union[int, NotFound, Empty]" = Empty(),
        observations: "Union[GQLObservationConnection, Empty]" = Empty(),
        external_case_id: "Union[str, Empty]" = Empty(),
        primary_case_procedures: "Union[list[GQLCaseProcedure], Empty]" = Empty(),
        case_flags: "Union[list[GQLCaseFlag], Empty]" = Empty(),
        service_line: "Union[GQLServiceLine, NotFound, Empty]" = Empty(),
        case_matching_status: "Union[GQLCaseMatchingStatus, Empty]" = Empty(),
        matching_status_reason: "Union[GQLMatchingStatusReason, NotFound, Empty]" = Empty(),
        event_notifications: "Union[Union[list[GQLEventNotification], NotFound], Empty]" = Empty(),
        case_labels: "Union[list[GQLCaseLabel], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._scheduled_start_time = scheduled_start_time
        self._scheduled_end_time = scheduled_end_time
        self._updated_time = updated_time
        self._created_time = created_time
        self._site = site
        self._room = room
        self._case_classification_type = case_classification_type
        self._status = status
        self._case_staff = case_staff
        self._case_staff_plan = case_staff_plan
        self._note_plan = note_plan
        self._staff = staff
        self._is_in_flip_room = is_in_flip_room
        self._preceding_case = preceding_case
        self._is_first_case = is_first_case
        self._case_procedures = case_procedures
        self._procedures = procedures
        self._is_add_on = is_add_on
        self._patient = patient
        self._patient_class = patient_class
        self._version = version
        self._observations = observations
        self._external_case_id = external_case_id
        self._primary_case_procedures = primary_case_procedures
        self._case_flags = case_flags
        self._service_line = service_line
        self._case_matching_status = case_matching_status
        self._matching_status_reason = matching_status_reason
        self._event_notifications = event_notifications
        self._case_labels = case_labels

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def scheduled_start_time(self) -> "datetime":
        if isinstance(self._scheduled_start_time, Empty):
            raise ValueError("Field scheduled_start_time was not selected as part of the query")
        return self._scheduled_start_time

    @property
    def scheduled_end_time(self) -> "datetime":
        if isinstance(self._scheduled_end_time, Empty):
            raise ValueError("Field scheduled_end_time was not selected as part of the query")
        return self._scheduled_end_time

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def case_classification_type(self) -> "Union[GQLCaseClassificationType, NotFound]":
        if isinstance(self._case_classification_type, Empty):
            raise ValueError("Field case_classification_type was not selected as part of the query")
        return self._case_classification_type

    @property
    def status(self) -> "str":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def case_staff(self) -> "list[GQLCaseStaff]":
        if isinstance(self._case_staff, Empty):
            raise ValueError("Field case_staff was not selected as part of the query")
        return self._case_staff

    @property
    def case_staff_plan(self) -> "GQLCaseStaffPlanConnection":
        if isinstance(self._case_staff_plan, Empty):
            raise ValueError("Field case_staff_plan was not selected as part of the query")
        return self._case_staff_plan

    @property
    def note_plan(self) -> "Union[GQLCaseNotePlan, NotFound]":
        if isinstance(self._note_plan, Empty):
            raise ValueError("Field note_plan was not selected as part of the query")
        return self._note_plan

    @property
    def staff(self) -> "GQLStaffConnection":
        if isinstance(self._staff, Empty):
            raise ValueError("Field staff was not selected as part of the query")
        return self._staff

    @property
    def is_in_flip_room(self) -> "Union[bool, NotFound]":
        if isinstance(self._is_in_flip_room, Empty):
            raise ValueError("Field is_in_flip_room was not selected as part of the query")
        return self._is_in_flip_room

    @property
    def preceding_case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._preceding_case, Empty):
            raise ValueError("Field preceding_case was not selected as part of the query")
        return self._preceding_case

    @property
    def is_first_case(self) -> "Union[bool, NotFound]":
        if isinstance(self._is_first_case, Empty):
            raise ValueError("Field is_first_case was not selected as part of the query")
        return self._is_first_case

    @property
    def case_procedures(self) -> "list[GQLCaseProcedure]":
        if isinstance(self._case_procedures, Empty):
            raise ValueError("Field case_procedures was not selected as part of the query")
        return self._case_procedures

    @property
    def procedures(self) -> "GQLProcedureConnection":
        if isinstance(self._procedures, Empty):
            raise ValueError("Field procedures was not selected as part of the query")
        return self._procedures

    @property
    def is_add_on(self) -> "Union[bool, NotFound]":
        if isinstance(self._is_add_on, Empty):
            raise ValueError("Field is_add_on was not selected as part of the query")
        return self._is_add_on

    @property
    def patient(self) -> "Union[GQLPatient, NotFound]":
        if isinstance(self._patient, Empty):
            raise ValueError("Field patient was not selected as part of the query")
        return self._patient

    @property
    def patient_class(self) -> "Union[GQLPatientClass, NotFound]":
        if isinstance(self._patient_class, Empty):
            raise ValueError("Field patient_class was not selected as part of the query")
        return self._patient_class

    @property
    def version(self) -> "Union[int, NotFound]":
        if isinstance(self._version, Empty):
            raise ValueError("Field version was not selected as part of the query")
        return self._version

    @property
    def observations(self) -> "GQLObservationConnection":
        if isinstance(self._observations, Empty):
            raise ValueError("Field observations was not selected as part of the query")
        return self._observations

    @property
    def external_case_id(self) -> "str":
        if isinstance(self._external_case_id, Empty):
            raise ValueError("Field external_case_id was not selected as part of the query")
        return self._external_case_id

    @property
    def primary_case_procedures(self) -> "list[GQLCaseProcedure]":
        if isinstance(self._primary_case_procedures, Empty):
            raise ValueError("Field primary_case_procedures was not selected as part of the query")
        return self._primary_case_procedures

    @property
    def case_flags(self) -> "list[GQLCaseFlag]":
        if isinstance(self._case_flags, Empty):
            raise ValueError("Field case_flags was not selected as part of the query")
        return self._case_flags

    @property
    def service_line(self) -> "Union[GQLServiceLine, NotFound]":
        if isinstance(self._service_line, Empty):
            raise ValueError("Field service_line was not selected as part of the query")
        return self._service_line

    @property
    def case_matching_status(self) -> "GQLCaseMatchingStatus":
        if isinstance(self._case_matching_status, Empty):
            raise ValueError("Field case_matching_status was not selected as part of the query")
        return self._case_matching_status

    @property
    def matching_status_reason(self) -> "Union[GQLMatchingStatusReason, NotFound]":
        if isinstance(self._matching_status_reason, Empty):
            raise ValueError("Field matching_status_reason was not selected as part of the query")
        return self._matching_status_reason

    @property
    def event_notifications(self) -> "Union[Union[list[GQLEventNotification], NotFound]]":
        if isinstance(self._event_notifications, Empty):
            raise ValueError("Field event_notifications was not selected as part of the query")
        return self._event_notifications

    @property
    def case_labels(self) -> "list[GQLCaseLabel]":
        if isinstance(self._case_labels, Empty):
            raise ValueError("Field case_labels was not selected as part of the query")
        return self._case_labels


class GQLCaseClassificationType(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        decription: "Union[str, NotFound, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._org_id = org_id
        self._name = name
        self._decription = decription
        self._created_time = created_time
        self._updated_time = updated_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def decription(self) -> "Union[str, NotFound]":
        if isinstance(self._decription, Empty):
            raise ValueError("Field decription was not selected as part of the query")
        return self._decription

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time


class GQLCaseStaff(GQLClientObject):
    def __init__(
        self,
        staff: "Union[GQLStaff, Empty]" = Empty(),
        role: "Union[str, NotFound, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
    ) -> None:
        self._staff = staff
        self._role = role
        self._created_time = created_time

    @property
    def staff(self) -> "GQLStaff":
        if isinstance(self._staff, Empty):
            raise ValueError("Field staff was not selected as part of the query")
        return self._staff

    @property
    def role(self) -> "Union[str, NotFound]":
        if isinstance(self._role, Empty):
            raise ValueError("Field role was not selected as part of the query")
        return self._role

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time


class GQLStaff(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        first_name: "Union[str, Empty]" = Empty(),
        last_name: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        external_staff_id: "Union[str, Empty]" = Empty(),
        codes: "Union[Union[list[GQLStaffCode], NotFound], Empty]" = Empty(),
        staff_event_notification_contact_information: "Union[GQLStaffEventNotificationContactInformationConnection, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        most_frequent_site: "Union[GQLSite, NotFound, Empty]" = Empty(),
        block_ids: "Union[Union[list[Union[str, Empty, None]], NotFound], Empty]" = Empty(),
        blocks: "Union[Union[list[Union[GQLBlock, Empty]], NotFound], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._first_name = first_name
        self._last_name = last_name
        self._name = name
        self._external_staff_id = external_staff_id
        self._codes = codes
        self._staff_event_notification_contact_information = (
            staff_event_notification_contact_information
        )
        self._organization = organization
        self._most_frequent_site = most_frequent_site
        self._block_ids = block_ids
        self._blocks = blocks

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def first_name(self) -> "str":
        if isinstance(self._first_name, Empty):
            raise ValueError("Field first_name was not selected as part of the query")
        return self._first_name

    @property
    def last_name(self) -> "str":
        if isinstance(self._last_name, Empty):
            raise ValueError("Field last_name was not selected as part of the query")
        return self._last_name

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def external_staff_id(self) -> "str":
        if isinstance(self._external_staff_id, Empty):
            raise ValueError("Field external_staff_id was not selected as part of the query")
        return self._external_staff_id

    @property
    def codes(self) -> "Union[Union[list[GQLStaffCode], NotFound]]":
        if isinstance(self._codes, Empty):
            raise ValueError("Field codes was not selected as part of the query")
        return self._codes

    @property
    def staff_event_notification_contact_information(
        self,
    ) -> "GQLStaffEventNotificationContactInformationConnection":
        if isinstance(self._staff_event_notification_contact_information, Empty):
            raise ValueError(
                "Field staff_event_notification_contact_information was not selected as part of the query"
            )
        return self._staff_event_notification_contact_information

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def most_frequent_site(self) -> "Union[GQLSite, NotFound]":
        if isinstance(self._most_frequent_site, Empty):
            raise ValueError("Field most_frequent_site was not selected as part of the query")
        return self._most_frequent_site

    @property
    def block_ids(self) -> "Union[Union[list[Union[str, Empty, None]], NotFound]]":
        if isinstance(self._block_ids, Empty):
            raise ValueError("Field block_ids was not selected as part of the query")
        return self._block_ids

    @property
    def blocks(self) -> "Union[Union[list[Union[GQLBlock, Empty]], NotFound]]":
        if isinstance(self._blocks, Empty):
            raise ValueError("Field blocks was not selected as part of the query")
        return self._blocks


class GQLStaffCode(GQLClientObject):
    def __init__(
        self,
        coding_system: "Union[str, Empty]" = Empty(),
        code: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._coding_system = coding_system
        self._code = code

    @property
    def coding_system(self) -> "str":
        if isinstance(self._coding_system, Empty):
            raise ValueError("Field coding_system was not selected as part of the query")
        return self._coding_system

    @property
    def code(self) -> "str":
        if isinstance(self._code, Empty):
            raise ValueError("Field code was not selected as part of the query")
        return self._code


class GQLStaffEventNotificationContactInformationConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLStaffEventNotificationContactInformationEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLStaffEventNotificationContactInformationEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLStaffEventNotificationContactInformationEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLStaffEventNotificationContactInformation, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLStaffEventNotificationContactInformation":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLStaffEventNotificationContactInformation(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        staff_id: "Union[str, Empty]" = Empty(),
        staff: "Union[GQLStaff, Empty]" = Empty(),
        event_type_id: "Union[str, Empty]" = Empty(),
        event_type: "Union[GQLEventType, Empty]" = Empty(),
        observation_type: "Union[GQLObservationType, NotFound, Empty]" = Empty(),
        contact_information: "Union[GQLContactInformation, Empty]" = Empty(),
        contact_information_value: "Union[str, Empty]" = Empty(),
        contact_information_type: "Union[GQLContactInformationType, Empty]" = Empty(),
        contact_information_initialized: "Union[bool, Empty]" = Empty(),
        event_notifications: "Union[list[GQLEventNotification], Empty]" = Empty(),
        staff_event_notifications: "Union[list[GQLEventNotification], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._staff_id = staff_id
        self._staff = staff
        self._event_type_id = event_type_id
        self._event_type = event_type
        self._observation_type = observation_type
        self._contact_information = contact_information
        self._contact_information_value = contact_information_value
        self._contact_information_type = contact_information_type
        self._contact_information_initialized = contact_information_initialized
        self._event_notifications = event_notifications
        self._staff_event_notifications = staff_event_notifications

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def staff_id(self) -> "str":
        if isinstance(self._staff_id, Empty):
            raise ValueError("Field staff_id was not selected as part of the query")
        return self._staff_id

    @property
    def staff(self) -> "GQLStaff":
        if isinstance(self._staff, Empty):
            raise ValueError("Field staff was not selected as part of the query")
        return self._staff

    @property
    def event_type_id(self) -> "str":
        if isinstance(self._event_type_id, Empty):
            raise ValueError("Field event_type_id was not selected as part of the query")
        return self._event_type_id

    @property
    def event_type(self) -> "GQLEventType":
        if isinstance(self._event_type, Empty):
            raise ValueError("Field event_type was not selected as part of the query")
        return self._event_type

    @property
    def observation_type(self) -> "Union[GQLObservationType, NotFound]":
        if isinstance(self._observation_type, Empty):
            raise ValueError("Field observation_type was not selected as part of the query")
        return self._observation_type

    @property
    def contact_information(self) -> "GQLContactInformation":
        if isinstance(self._contact_information, Empty):
            raise ValueError("Field contact_information was not selected as part of the query")
        return self._contact_information

    @property
    def contact_information_value(self) -> "str":
        if isinstance(self._contact_information_value, Empty):
            raise ValueError(
                "Field contact_information_value was not selected as part of the query"
            )
        return self._contact_information_value

    @property
    def contact_information_type(self) -> "GQLContactInformationType":
        if isinstance(self._contact_information_type, Empty):
            raise ValueError("Field contact_information_type was not selected as part of the query")
        return self._contact_information_type

    @property
    def contact_information_initialized(self) -> "bool":
        if isinstance(self._contact_information_initialized, Empty):
            raise ValueError(
                "Field contact_information_initialized was not selected as part of the query"
            )
        return self._contact_information_initialized

    @property
    def event_notifications(self) -> "list[GQLEventNotification]":
        if isinstance(self._event_notifications, Empty):
            raise ValueError("Field event_notifications was not selected as part of the query")
        return self._event_notifications

    @property
    def staff_event_notifications(self) -> "list[GQLEventNotification]":
        if isinstance(self._staff_event_notifications, Empty):
            raise ValueError(
                "Field staff_event_notifications was not selected as part of the query"
            )
        return self._staff_event_notifications


class GQLEventType(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        type: "Union[str, Empty]" = Empty(),
        name: "Union[str, NotFound, Empty]" = Empty(),
        description: "Union[str, NotFound, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
        hidden: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._type = type
        self._name = name
        self._description = description
        self._color = color
        self._hidden = hidden

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def type(self) -> "str":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def name(self) -> "Union[str, NotFound]":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def description(self) -> "Union[str, NotFound]":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def hidden(self) -> "bool":
        if isinstance(self._hidden, Empty):
            raise ValueError("Field hidden was not selected as part of the query")
        return self._hidden


class GQLObservationType(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        description: "Union[str, NotFound, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._description = description
        self._color = color
        self._created_time = created_time
        self._updated_time = updated_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def description(self) -> "Union[str, NotFound]":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time


class GQLContactInformation(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        contact_information_value: "Union[str, NotFound, Empty]" = Empty(),
        type: "Union[GQLContactInformationType, NotFound, Empty]" = Empty(),
        initial_notification_sent: "Union[datetime, NotFound, Empty]" = Empty(),
        first_name: "Union[str, NotFound, Empty]" = Empty(),
        last_name: "Union[str, NotFound, Empty]" = Empty(),
        staff_event_contact_information: "Union[Union[list[GQLStaffEventNotificationContactInformation], NotFound], Empty]" = Empty(),
        is_apella_employee: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._contact_information_value = contact_information_value
        self._type = type
        self._initial_notification_sent = initial_notification_sent
        self._first_name = first_name
        self._last_name = last_name
        self._staff_event_contact_information = staff_event_contact_information
        self._is_apella_employee = is_apella_employee

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def contact_information_value(self) -> "Union[str, NotFound]":
        if isinstance(self._contact_information_value, Empty):
            raise ValueError(
                "Field contact_information_value was not selected as part of the query"
            )
        return self._contact_information_value

    @property
    def type(self) -> "Union[GQLContactInformationType, NotFound]":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def initial_notification_sent(self) -> "Union[datetime, NotFound]":
        if isinstance(self._initial_notification_sent, Empty):
            raise ValueError(
                "Field initial_notification_sent was not selected as part of the query"
            )
        return self._initial_notification_sent

    @property
    def first_name(self) -> "Union[str, NotFound]":
        if isinstance(self._first_name, Empty):
            raise ValueError("Field first_name was not selected as part of the query")
        return self._first_name

    @property
    def last_name(self) -> "Union[str, NotFound]":
        if isinstance(self._last_name, Empty):
            raise ValueError("Field last_name was not selected as part of the query")
        return self._last_name

    @property
    def staff_event_contact_information(
        self,
    ) -> "Union[Union[list[GQLStaffEventNotificationContactInformation], NotFound]]":
        if isinstance(self._staff_event_contact_information, Empty):
            raise ValueError(
                "Field staff_event_contact_information was not selected as part of the query"
            )
        return self._staff_event_contact_information

    @property
    def is_apella_employee(self) -> "Union[bool, NotFound]":
        if isinstance(self._is_apella_employee, Empty):
            raise ValueError("Field is_apella_employee was not selected as part of the query")
        return self._is_apella_employee


class GQLEventNotification(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        event: "Union[GQLEvent, NotFound, Empty]" = Empty(),
        observation: "Union[GQLObservation, NotFound, Empty]" = Empty(),
        message_id: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        event_time: "Union[datetime, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        sent_time: "Union[datetime, NotFound, Empty]" = Empty(),
        staff_event_contact_information: "Union[GQLStaffEventNotificationContactInformation, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._event = event
        self._observation = observation
        self._message_id = message_id
        self._case = case
        self._event_time = event_time
        self._created_time = created_time
        self._sent_time = sent_time
        self._staff_event_contact_information = staff_event_contact_information

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def event(self) -> "Union[GQLEvent, NotFound]":
        if isinstance(self._event, Empty):
            raise ValueError("Field event was not selected as part of the query")
        return self._event

    @property
    def observation(self) -> "Union[GQLObservation, NotFound]":
        if isinstance(self._observation, Empty):
            raise ValueError("Field observation was not selected as part of the query")
        return self._observation

    @property
    def message_id(self) -> "str":
        if isinstance(self._message_id, Empty):
            raise ValueError("Field message_id was not selected as part of the query")
        return self._message_id

    @property
    def case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def event_time(self) -> "datetime":
        if isinstance(self._event_time, Empty):
            raise ValueError("Field event_time was not selected as part of the query")
        return self._event_time

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def sent_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sent_time, Empty):
            raise ValueError("Field sent_time was not selected as part of the query")
        return self._sent_time

    @property
    def staff_event_contact_information(self) -> "GQLStaffEventNotificationContactInformation":
        if isinstance(self._staff_event_contact_information, Empty):
            raise ValueError(
                "Field staff_event_contact_information was not selected as part of the query"
            )
        return self._staff_event_contact_information


class GQLEvent(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        type: "Union[str, NotFound, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        label: "Union[str, NotFound, Empty]" = Empty(),
        color: "Union[str, NotFound, Empty]" = Empty(),
        description: "Union[str, NotFound, Empty]" = Empty(),
        hidden: "Union[bool, NotFound, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        labels: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        notes: "Union[str, NotFound, Empty]" = Empty(),
        source: "Union[str, Empty]" = Empty(),
        source_type: "Union[str, Empty]" = Empty(),
        model_version: "Union[str, NotFound, Empty]" = Empty(),
        confidence: "Union[float, NotFound, Empty]" = Empty(),
        camera_id: "Union[str, NotFound, Empty]" = Empty(),
        version: "Union[int, NotFound, Empty]" = Empty(),
        deleted_at: "Union[datetime, NotFound, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
        attrs: "Union[GQLEventType, NotFound, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        etag: "Union[str, NotFound, Empty]" = Empty(),
        event_matching_status: "Union[GQLEventMatchingStatus, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._type = type
        self._name = name
        self._label = label
        self._color = color
        self._description = description
        self._hidden = hidden
        self._start_time = start_time
        self._labels = labels
        self._notes = notes
        self._source = source
        self._source_type = source_type
        self._model_version = model_version
        self._confidence = confidence
        self._camera_id = camera_id
        self._version = version
        self._deleted_at = deleted_at
        self._updated_time = updated_time
        self._attrs = attrs
        self._organization = organization
        self._site = site
        self._room = room
        self._etag = etag
        self._event_matching_status = event_matching_status

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def type(self) -> "Union[str, NotFound]":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def label(self) -> "Union[str, NotFound]":
        if isinstance(self._label, Empty):
            raise ValueError("Field label was not selected as part of the query")
        return self._label

    @property
    def color(self) -> "Union[str, NotFound]":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def description(self) -> "Union[str, NotFound]":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description

    @property
    def hidden(self) -> "Union[bool, NotFound]":
        if isinstance(self._hidden, Empty):
            raise ValueError("Field hidden was not selected as part of the query")
        return self._hidden

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def labels(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._labels, Empty):
            raise ValueError("Field labels was not selected as part of the query")
        return self._labels

    @property
    def notes(self) -> "Union[str, NotFound]":
        if isinstance(self._notes, Empty):
            raise ValueError("Field notes was not selected as part of the query")
        return self._notes

    @property
    def source(self) -> "str":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def source_type(self) -> "str":
        if isinstance(self._source_type, Empty):
            raise ValueError("Field source_type was not selected as part of the query")
        return self._source_type

    @property
    def model_version(self) -> "Union[str, NotFound]":
        if isinstance(self._model_version, Empty):
            raise ValueError("Field model_version was not selected as part of the query")
        return self._model_version

    @property
    def confidence(self) -> "Union[float, NotFound]":
        if isinstance(self._confidence, Empty):
            raise ValueError("Field confidence was not selected as part of the query")
        return self._confidence

    @property
    def camera_id(self) -> "Union[str, NotFound]":
        if isinstance(self._camera_id, Empty):
            raise ValueError("Field camera_id was not selected as part of the query")
        return self._camera_id

    @property
    def version(self) -> "Union[int, NotFound]":
        if isinstance(self._version, Empty):
            raise ValueError("Field version was not selected as part of the query")
        return self._version

    @property
    def deleted_at(self) -> "Union[datetime, NotFound]":
        if isinstance(self._deleted_at, Empty):
            raise ValueError("Field deleted_at was not selected as part of the query")
        return self._deleted_at

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def attrs(self) -> "Union[GQLEventType, NotFound]":
        if isinstance(self._attrs, Empty):
            raise ValueError("Field attrs was not selected as part of the query")
        return self._attrs

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def etag(self) -> "Union[str, NotFound]":
        if isinstance(self._etag, Empty):
            raise ValueError("Field etag was not selected as part of the query")
        return self._etag

    @property
    def event_matching_status(self) -> "Union[GQLEventMatchingStatus, NotFound]":
        if isinstance(self._event_matching_status, Empty):
            raise ValueError("Field event_matching_status was not selected as part of the query")
        return self._event_matching_status


class GQLObservation(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        observation_time: "Union[datetime, Empty]" = Empty(),
        recorded_time: "Union[datetime, NotFound, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, Empty]" = Empty(),
        type: "Union[str, Empty]" = Empty(),
        observation_type: "Union[GQLObservationType, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._observation_time = observation_time
        self._recorded_time = recorded_time
        self._organization = organization
        self._case = case
        self._type = type
        self._observation_type = observation_type

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def observation_time(self) -> "datetime":
        if isinstance(self._observation_time, Empty):
            raise ValueError("Field observation_time was not selected as part of the query")
        return self._observation_time

    @property
    def recorded_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._recorded_time, Empty):
            raise ValueError("Field recorded_time was not selected as part of the query")
        return self._recorded_time

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def case(self) -> "GQLScheduledCase":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def type(self) -> "str":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def observation_type(self) -> "GQLObservationType":
        if isinstance(self._observation_type, Empty):
            raise ValueError("Field observation_type was not selected as part of the query")
        return self._observation_type


class GQLBlock(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        site_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        block_times: "Union[list[GQLBlockTime], Empty]" = Empty(),
        surgeons: "Union[list[GQLStaff], Empty]" = Empty(),
        surgeon_ids: "Union[list[str], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._color = color
        self._archived_time = archived_time
        self._org_id = org_id
        self._site_ids = site_ids
        self._organization = organization
        self._block_times = block_times
        self._surgeons = surgeons
        self._surgeon_ids = surgeon_ids

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def site_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._site_ids, Empty):
            raise ValueError("Field site_ids was not selected as part of the query")
        return self._site_ids

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def block_times(self) -> "list[GQLBlockTime]":
        if isinstance(self._block_times, Empty):
            raise ValueError("Field block_times was not selected as part of the query")
        return self._block_times

    @property
    def surgeons(self) -> "list[GQLStaff]":
        if isinstance(self._surgeons, Empty):
            raise ValueError("Field surgeons was not selected as part of the query")
        return self._surgeons

    @property
    def surgeon_ids(self) -> "list[str]":
        if isinstance(self._surgeon_ids, Empty):
            raise ValueError("Field surgeon_ids was not selected as part of the query")
        return self._surgeon_ids


class GQLBlockTime(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        block_id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        block: "Union[GQLBlock, Empty]" = Empty(),
        released_from: "Union[str, NotFound, Empty]" = Empty(),
        created_time: "Union[datetime, NotFound, Empty]" = Empty(),
        releases: "Union[list[GQLBlockTimeRelease], Empty]" = Empty(),
        surgeon_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        available_intervals: "Union[Union[list[GQLBlockTimeAvailableInterval], NotFound], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._block_id = block_id
        self._start_time = start_time
        self._end_time = end_time
        self._room_id = room_id
        self._room = room
        self._block = block
        self._released_from = released_from
        self._created_time = created_time
        self._releases = releases
        self._surgeon_ids = surgeon_ids
        self._available_intervals = available_intervals

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def block_id(self) -> "str":
        if isinstance(self._block_id, Empty):
            raise ValueError("Field block_id was not selected as part of the query")
        return self._block_id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def block(self) -> "GQLBlock":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block

    @property
    def released_from(self) -> "Union[str, NotFound]":
        if isinstance(self._released_from, Empty):
            raise ValueError("Field released_from was not selected as part of the query")
        return self._released_from

    @property
    def created_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def releases(self) -> "list[GQLBlockTimeRelease]":
        if isinstance(self._releases, Empty):
            raise ValueError("Field releases was not selected as part of the query")
        return self._releases

    @property
    def surgeon_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._surgeon_ids, Empty):
            raise ValueError("Field surgeon_ids was not selected as part of the query")
        return self._surgeon_ids

    @property
    def available_intervals(self) -> "Union[Union[list[GQLBlockTimeAvailableInterval], NotFound]]":
        if isinstance(self._available_intervals, Empty):
            raise ValueError("Field available_intervals was not selected as part of the query")
        return self._available_intervals


class GQLBlockTimeRelease(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        block_time_id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        block_time: "Union[GQLBlockTime, Empty]" = Empty(),
        reason: "Union[str, Empty]" = Empty(),
        released_at: "Union[datetime, Empty]" = Empty(),
        released_time: "Union[datetime, Empty]" = Empty(),
        source: "Union[str, Empty]" = Empty(),
        source_type: "Union[str, Empty]" = Empty(),
        unreleased_time: "Union[datetime, NotFound, Empty]" = Empty(),
        unreleased_source: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._block_time_id = block_time_id
        self._start_time = start_time
        self._end_time = end_time
        self._block_time = block_time
        self._reason = reason
        self._released_at = released_at
        self._released_time = released_time
        self._source = source
        self._source_type = source_type
        self._unreleased_time = unreleased_time
        self._unreleased_source = unreleased_source

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def block_time_id(self) -> "str":
        if isinstance(self._block_time_id, Empty):
            raise ValueError("Field block_time_id was not selected as part of the query")
        return self._block_time_id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def block_time(self) -> "GQLBlockTime":
        if isinstance(self._block_time, Empty):
            raise ValueError("Field block_time was not selected as part of the query")
        return self._block_time

    @property
    def reason(self) -> "str":
        if isinstance(self._reason, Empty):
            raise ValueError("Field reason was not selected as part of the query")
        return self._reason

    @property
    def released_at(self) -> "datetime":
        if isinstance(self._released_at, Empty):
            raise ValueError("Field released_at was not selected as part of the query")
        return self._released_at

    @property
    def released_time(self) -> "datetime":
        if isinstance(self._released_time, Empty):
            raise ValueError("Field released_time was not selected as part of the query")
        return self._released_time

    @property
    def source(self) -> "str":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def source_type(self) -> "str":
        if isinstance(self._source_type, Empty):
            raise ValueError("Field source_type was not selected as part of the query")
        return self._source_type

    @property
    def unreleased_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._unreleased_time, Empty):
            raise ValueError("Field unreleased_time was not selected as part of the query")
        return self._unreleased_time

    @property
    def unreleased_source(self) -> "Union[str, NotFound]":
        if isinstance(self._unreleased_source, Empty):
            raise ValueError("Field unreleased_source was not selected as part of the query")
        return self._unreleased_source


class GQLBlockTimeAvailableInterval(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        block_time_id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        surgeon_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._block_time_id = block_time_id
        self._start_time = start_time
        self._end_time = end_time
        self._room_id = room_id
        self._surgeon_ids = surgeon_ids

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def block_time_id(self) -> "str":
        if isinstance(self._block_time_id, Empty):
            raise ValueError("Field block_time_id was not selected as part of the query")
        return self._block_time_id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def surgeon_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._surgeon_ids, Empty):
            raise ValueError("Field surgeon_ids was not selected as part of the query")
        return self._surgeon_ids


class GQLCaseStaffPlanConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseStaffPlanEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseStaffPlanEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseStaffPlanEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseStaffPlan, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseStaffPlan":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseStaffPlan(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        staff: "Union[GQLStaff, NotFound, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        role: "Union[str, NotFound, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._staff = staff
        self._case = case
        self._role = role
        self._site = site
        self._organization = organization
        self._created_time = created_time
        self._archived_time = archived_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def staff(self) -> "Union[GQLStaff, NotFound]":
        if isinstance(self._staff, Empty):
            raise ValueError("Field staff was not selected as part of the query")
        return self._staff

    @property
    def case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def role(self) -> "Union[str, NotFound]":
        if isinstance(self._role, Empty):
            raise ValueError("Field role was not selected as part of the query")
        return self._role

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time


class GQLCaseNotePlan(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, Empty]" = Empty(),
        note: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._case = case
        self._note = note
        self._site = site
        self._organization = organization
        self._created_time = created_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def case(self) -> "GQLScheduledCase":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def note(self) -> "str":
        if isinstance(self._note, Empty):
            raise ValueError("Field note was not selected as part of the query")
        return self._note

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time


class GQLStaffConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLStaffEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLStaffEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLStaffEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLStaff, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLStaff":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseProcedure(GQLClientObject):
    def __init__(
        self,
        procedure: "Union[GQLProcedure, Empty]" = Empty(),
        hierarchy: "Union[int, NotFound, Empty]" = Empty(),
        anesthesia: "Union[GQLAnesthesia, NotFound, Empty]" = Empty(),
    ) -> None:
        self._procedure = procedure
        self._hierarchy = hierarchy
        self._anesthesia = anesthesia

    @property
    def procedure(self) -> "GQLProcedure":
        if isinstance(self._procedure, Empty):
            raise ValueError("Field procedure was not selected as part of the query")
        return self._procedure

    @property
    def hierarchy(self) -> "Union[int, NotFound]":
        if isinstance(self._hierarchy, Empty):
            raise ValueError("Field hierarchy was not selected as part of the query")
        return self._hierarchy

    @property
    def anesthesia(self) -> "Union[GQLAnesthesia, NotFound]":
        if isinstance(self._anesthesia, Empty):
            raise ValueError("Field anesthesia was not selected as part of the query")
        return self._anesthesia


class GQLProcedure(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        hierarchy: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._organization = organization
        self._name = name
        self._hierarchy = hierarchy

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def hierarchy(self) -> "Union[int, NotFound]":
        if isinstance(self._hierarchy, Empty):
            raise ValueError("Field hierarchy was not selected as part of the query")
        return self._hierarchy


class GQLAnesthesia(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._org_id = org_id

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id


class GQLProcedureConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLProcedureEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLProcedureEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLProcedureEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLProcedure, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLProcedure":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLPatient(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        personal_info: "Union[GQLPersonalInfo, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._personal_info = personal_info

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def personal_info(self) -> "Union[GQLPersonalInfo, NotFound]":
        if isinstance(self._personal_info, Empty):
            raise ValueError("Field personal_info was not selected as part of the query")
        return self._personal_info


class GQLPersonalInfo(GQLClientObject):
    def __init__(
        self,
        first_name_abbreviated: "Union[str, Empty]" = Empty(),
        last_name_abbreviated: "Union[str, Empty]" = Empty(),
        fullname: "Union[str, NotFound, Empty]" = Empty(),
        age_range: "Union[GQLAgeRange, NotFound, Empty]" = Empty(),
        age: "Union[int, NotFound, Empty]" = Empty(),
        administrative_sex: "Union[GQLAdministrativeSex, NotFound, Empty]" = Empty(),
    ) -> None:
        self._first_name_abbreviated = first_name_abbreviated
        self._last_name_abbreviated = last_name_abbreviated
        self._fullname = fullname
        self._age_range = age_range
        self._age = age
        self._administrative_sex = administrative_sex

    @property
    def first_name_abbreviated(self) -> "str":
        if isinstance(self._first_name_abbreviated, Empty):
            raise ValueError("Field first_name_abbreviated was not selected as part of the query")
        return self._first_name_abbreviated

    @property
    def last_name_abbreviated(self) -> "str":
        if isinstance(self._last_name_abbreviated, Empty):
            raise ValueError("Field last_name_abbreviated was not selected as part of the query")
        return self._last_name_abbreviated

    @property
    def fullname(self) -> "Union[str, NotFound]":
        if isinstance(self._fullname, Empty):
            raise ValueError("Field fullname was not selected as part of the query")
        return self._fullname

    @property
    def age_range(self) -> "Union[GQLAgeRange, NotFound]":
        if isinstance(self._age_range, Empty):
            raise ValueError("Field age_range was not selected as part of the query")
        return self._age_range

    @property
    def age(self) -> "Union[int, NotFound]":
        if isinstance(self._age, Empty):
            raise ValueError("Field age was not selected as part of the query")
        return self._age

    @property
    def administrative_sex(self) -> "Union[GQLAdministrativeSex, NotFound]":
        if isinstance(self._administrative_sex, Empty):
            raise ValueError("Field administrative_sex was not selected as part of the query")
        return self._administrative_sex


class GQLAgeRange(GQLClientObject):
    def __init__(
        self,
        min: "Union[int, NotFound, Empty]" = Empty(),
        max: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._min = min
        self._max = max

    @property
    def min(self) -> "Union[int, NotFound]":
        if isinstance(self._min, Empty):
            raise ValueError("Field min was not selected as part of the query")
        return self._min

    @property
    def max(self) -> "Union[int, NotFound]":
        if isinstance(self._max, Empty):
            raise ValueError("Field max was not selected as part of the query")
        return self._max


class GQLAdministrativeSex(GQLClientObject):
    def __init__(
        self,
        type: "Union[GQLAdministrativeSexType, Empty]" = Empty(),
        text: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._type = type
        self._text = text

    @property
    def type(self) -> "GQLAdministrativeSexType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def text(self) -> "Union[str, NotFound]":
        if isinstance(self._text, Empty):
            raise ValueError("Field text was not selected as part of the query")
        return self._text


class GQLObservationConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLObservationEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLObservationEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLObservationEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLObservation, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLObservation":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseFlag(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        flag_type: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._flag_type = flag_type
        self._case = case
        self._created_time = created_time
        self._updated_time = updated_time
        self._archived_time = archived_time
        self._organization = organization
        self._site = site

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def flag_type(self) -> "str":
        if isinstance(self._flag_type, Empty):
            raise ValueError("Field flag_type was not selected as part of the query")
        return self._flag_type

    @property
    def case(self) -> "GQLScheduledCase":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site


class GQLServiceLine(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, NotFound, Empty]" = Empty(),
        external_service_line_id: "Union[str, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        org: "Union[GQLOrganization, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._external_service_line_id = external_service_line_id
        self._org_id = org_id
        self._org = org

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "Union[str, NotFound]":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def external_service_line_id(self) -> "str":
        if isinstance(self._external_service_line_id, Empty):
            raise ValueError("Field external_service_line_id was not selected as part of the query")
        return self._external_service_line_id

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def org(self) -> "GQLOrganization":
        if isinstance(self._org, Empty):
            raise ValueError("Field org was not selected as part of the query")
        return self._org


class GQLMatchingStatusReason(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        explanation_for_change: "Union[str, Empty]" = Empty(),
        case_matching_status: "Union[GQLCaseMatchingStatus, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._explanation_for_change = explanation_for_change
        self._case_matching_status = case_matching_status

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def explanation_for_change(self) -> "str":
        if isinstance(self._explanation_for_change, Empty):
            raise ValueError("Field explanation_for_change was not selected as part of the query")
        return self._explanation_for_change

    @property
    def case_matching_status(self) -> "GQLCaseMatchingStatus":
        if isinstance(self._case_matching_status, Empty):
            raise ValueError("Field case_matching_status was not selected as part of the query")
        return self._case_matching_status


class GQLCaseLabel(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        field_id: "Union[str, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
        abbreviation: "Union[str, Empty]" = Empty(),
        value: "Union[str, Empty]" = Empty(),
        boolean_value: "Union[bool, NotFound, Empty]" = Empty(),
        case_id: "Union[str, Empty]" = Empty(),
        option_id: "Union[str, Empty]" = Empty(),
        updated_by_user_id: "Union[str, Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._field_id = field_id
        self._color = color
        self._abbreviation = abbreviation
        self._value = value
        self._boolean_value = boolean_value
        self._case_id = case_id
        self._option_id = option_id
        self._updated_by_user_id = updated_by_user_id
        self._archived_time = archived_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def field_id(self) -> "str":
        if isinstance(self._field_id, Empty):
            raise ValueError("Field field_id was not selected as part of the query")
        return self._field_id

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def abbreviation(self) -> "str":
        if isinstance(self._abbreviation, Empty):
            raise ValueError("Field abbreviation was not selected as part of the query")
        return self._abbreviation

    @property
    def value(self) -> "str":
        if isinstance(self._value, Empty):
            raise ValueError("Field value was not selected as part of the query")
        return self._value

    @property
    def boolean_value(self) -> "Union[bool, NotFound]":
        if isinstance(self._boolean_value, Empty):
            raise ValueError("Field boolean_value was not selected as part of the query")
        return self._boolean_value

    @property
    def case_id(self) -> "str":
        if isinstance(self._case_id, Empty):
            raise ValueError("Field case_id was not selected as part of the query")
        return self._case_id

    @property
    def option_id(self) -> "str":
        if isinstance(self._option_id, Empty):
            raise ValueError("Field option_id was not selected as part of the query")
        return self._option_id

    @property
    def updated_by_user_id(self) -> "str":
        if isinstance(self._updated_by_user_id, Empty):
            raise ValueError("Field updated_by_user_id was not selected as part of the query")
        return self._updated_by_user_id

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time


class GQLPhase(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        type_id: "Union[str, Empty]" = Empty(),
        phase_detail: "Union[GQLPhaseTypeRecord, Empty]" = Empty(),
        source_type: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        child_phases: "Union[Union[list[GQLPhase], NotFound], Empty]" = Empty(),
        parent_phases: "Union[Union[list[GQLPhase], NotFound], Empty]" = Empty(),
        start_event: "Union[GQLEvent, Empty]" = Empty(),
        end_event: "Union[GQLEvent, NotFound, Empty]" = Empty(),
        duration: "Union[timedelta, NotFound, Empty]" = Empty(),
        has_video_available: "Union[bool, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, NotFound, Empty]" = Empty(),
        status: "Union[GQLPhaseStatus, Empty]" = Empty(),
        invalidation_reason: "Union[str, NotFound, Empty]" = Empty(),
        time_range_verified: "Union[bool, Empty]" = Empty(),
        etag: "Union[str, NotFound, Empty]" = Empty(),
        event_matching_status: "Union[GQLEventMatchingStatus, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._organization = organization
        self._site = site
        self._room = room
        self._type_id = type_id
        self._phase_detail = phase_detail
        self._source_type = source_type
        self._case = case
        self._child_phases = child_phases
        self._parent_phases = parent_phases
        self._start_event = start_event
        self._end_event = end_event
        self._duration = duration
        self._has_video_available = has_video_available
        self._start_time = start_time
        self._end_time = end_time
        self._created_time = created_time
        self._updated_time = updated_time
        self._status = status
        self._invalidation_reason = invalidation_reason
        self._time_range_verified = time_range_verified
        self._etag = etag
        self._event_matching_status = event_matching_status

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def type_id(self) -> "str":
        if isinstance(self._type_id, Empty):
            raise ValueError("Field type_id was not selected as part of the query")
        return self._type_id

    @property
    def phase_detail(self) -> "GQLPhaseTypeRecord":
        if isinstance(self._phase_detail, Empty):
            raise ValueError("Field phase_detail was not selected as part of the query")
        return self._phase_detail

    @property
    def source_type(self) -> "str":
        if isinstance(self._source_type, Empty):
            raise ValueError("Field source_type was not selected as part of the query")
        return self._source_type

    @property
    def case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def child_phases(self) -> "Union[Union[list[GQLPhase], NotFound]]":
        if isinstance(self._child_phases, Empty):
            raise ValueError("Field child_phases was not selected as part of the query")
        return self._child_phases

    @property
    def parent_phases(self) -> "Union[Union[list[GQLPhase], NotFound]]":
        if isinstance(self._parent_phases, Empty):
            raise ValueError("Field parent_phases was not selected as part of the query")
        return self._parent_phases

    @property
    def start_event(self) -> "GQLEvent":
        if isinstance(self._start_event, Empty):
            raise ValueError("Field start_event was not selected as part of the query")
        return self._start_event

    @property
    def end_event(self) -> "Union[GQLEvent, NotFound]":
        if isinstance(self._end_event, Empty):
            raise ValueError("Field end_event was not selected as part of the query")
        return self._end_event

    @property
    def duration(self) -> "Union[timedelta, NotFound]":
        if isinstance(self._duration, Empty):
            raise ValueError("Field duration was not selected as part of the query")
        return self._duration

    @property
    def has_video_available(self) -> "bool":
        if isinstance(self._has_video_available, Empty):
            raise ValueError("Field has_video_available was not selected as part of the query")
        return self._has_video_available

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def updated_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def status(self) -> "GQLPhaseStatus":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def invalidation_reason(self) -> "Union[str, NotFound]":
        if isinstance(self._invalidation_reason, Empty):
            raise ValueError("Field invalidation_reason was not selected as part of the query")
        return self._invalidation_reason

    @property
    def time_range_verified(self) -> "bool":
        if isinstance(self._time_range_verified, Empty):
            raise ValueError("Field time_range_verified was not selected as part of the query")
        return self._time_range_verified

    @property
    def etag(self) -> "Union[str, NotFound]":
        if isinstance(self._etag, Empty):
            raise ValueError("Field etag was not selected as part of the query")
        return self._etag

    @property
    def event_matching_status(self) -> "Union[GQLEventMatchingStatus, NotFound]":
        if isinstance(self._event_matching_status, Empty):
            raise ValueError("Field event_matching_status was not selected as part of the query")
        return self._event_matching_status


class GQLPhaseTypeRecord(GQLClientObject):
    def __init__(
        self,
        type: "Union[GQLPhaseType, Empty]" = Empty(),
        title: "Union[str, Empty]" = Empty(),
        slug: "Union[str, Empty]" = Empty(),
        description: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._type = type
        self._title = title
        self._slug = slug
        self._description = description

    @property
    def type(self) -> "GQLPhaseType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def title(self) -> "str":
        if isinstance(self._title, Empty):
            raise ValueError("Field title was not selected as part of the query")
        return self._title

    @property
    def slug(self) -> "str":
        if isinstance(self._slug, Empty):
            raise ValueError("Field slug was not selected as part of the query")
        return self._slug

    @property
    def description(self) -> "str":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description


class GQLCaseForecast(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, Empty]" = Empty(),
        forecast_start_time: "Union[datetime, Empty]" = Empty(),
        forecast_end_time: "Union[datetime, Empty]" = Empty(),
        forecast_variant: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._case = case
        self._forecast_start_time = forecast_start_time
        self._forecast_end_time = forecast_end_time
        self._forecast_variant = forecast_variant
        self._room = room
        self._site = site
        self._organization = organization

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def case(self) -> "GQLScheduledCase":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case

    @property
    def forecast_start_time(self) -> "datetime":
        if isinstance(self._forecast_start_time, Empty):
            raise ValueError("Field forecast_start_time was not selected as part of the query")
        return self._forecast_start_time

    @property
    def forecast_end_time(self) -> "datetime":
        if isinstance(self._forecast_end_time, Empty):
            raise ValueError("Field forecast_end_time was not selected as part of the query")
        return self._forecast_end_time

    @property
    def forecast_variant(self) -> "str":
        if isinstance(self._forecast_variant, Empty):
            raise ValueError("Field forecast_variant was not selected as part of the query")
        return self._forecast_variant

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization


class GQLApellaCaseStatus(GQLClientObject):
    def __init__(
        self,
        name: "Union[GQLCaseStatusName, Empty]" = Empty(),
        source: "Union[GQLCaseSource, Empty]" = Empty(),
        since: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._name = name
        self._source = source
        self._since = since

    @property
    def name(self) -> "GQLCaseStatusName":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def source(self) -> "GQLCaseSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def since(self) -> "Union[datetime, NotFound]":
        if isinstance(self._since, Empty):
            raise ValueError("Field since was not selected as part of the query")
        return self._since


class GQLTurnover(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        type: "Union[GQLTurnoverType, Empty]" = Empty(),
        status: "Union[GQLTurnoverStatusGraphene, NotFound, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        preceding_case: "Union[GQLApellaCase, Empty]" = Empty(),
        following_case: "Union[GQLApellaCase, Empty]" = Empty(),
        meets_inclusion_criteria: "Union[bool, Empty]" = Empty(),
        labels: "Union[Union[list[GQLTurnoverLabel], NotFound], Empty]" = Empty(),
        note: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._type = type
        self._status = status
        self._start_time = start_time
        self._end_time = end_time
        self._preceding_case = preceding_case
        self._following_case = following_case
        self._meets_inclusion_criteria = meets_inclusion_criteria
        self._labels = labels
        self._note = note

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def type(self) -> "GQLTurnoverType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def status(self) -> "Union[GQLTurnoverStatusGraphene, NotFound]":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def preceding_case(self) -> "GQLApellaCase":
        if isinstance(self._preceding_case, Empty):
            raise ValueError("Field preceding_case was not selected as part of the query")
        return self._preceding_case

    @property
    def following_case(self) -> "GQLApellaCase":
        if isinstance(self._following_case, Empty):
            raise ValueError("Field following_case was not selected as part of the query")
        return self._following_case

    @property
    def meets_inclusion_criteria(self) -> "bool":
        if isinstance(self._meets_inclusion_criteria, Empty):
            raise ValueError("Field meets_inclusion_criteria was not selected as part of the query")
        return self._meets_inclusion_criteria

    @property
    def labels(self) -> "Union[Union[list[GQLTurnoverLabel], NotFound]]":
        if isinstance(self._labels, Empty):
            raise ValueError("Field labels was not selected as part of the query")
        return self._labels

    @property
    def note(self) -> "Union[str, NotFound]":
        if isinstance(self._note, Empty):
            raise ValueError("Field note was not selected as part of the query")
        return self._note


class GQLTurnoverStatusGraphene(GQLClientObject):
    def __init__(
        self,
        name: "Union[GQLTurnoverStatusName, Empty]" = Empty(),
        since: "Union[datetime, NotFound, Empty]" = Empty(),
    ) -> None:
        self._name = name
        self._since = since

    @property
    def name(self) -> "GQLTurnoverStatusName":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def since(self) -> "Union[datetime, NotFound]":
        if isinstance(self._since, Empty):
            raise ValueError("Field since was not selected as part of the query")
        return self._since


class GQLTurnoverLabel(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        type: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._type = type

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def type(self) -> "str":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type


class GQLApellaCaseConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLApellaCaseEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLApellaCaseEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLApellaCaseEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLApellaCase, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLApellaCase":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLEventConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLEventEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLEventEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLEventEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLEvent, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLEvent":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLBlockTimeConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLBlockTimeEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLBlockTimeEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLBlockTimeEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLBlockTime, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLBlockTime":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLRoomTag(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        organization_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._org_id = org_id
        self._organization_id = organization_id
        self._organization = organization
        self._name = name
        self._color = color

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def organization_id(self) -> "str":
        if isinstance(self._organization_id, Empty):
            raise ValueError("Field organization_id was not selected as part of the query")
        return self._organization_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color


class GQLRoomClosureConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLRoomClosureEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLRoomClosureEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLRoomClosureEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLRoomClosure, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLRoomClosure":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLRoomClosure(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        reason: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._room_id = room_id
        self._room = room
        self._start_time = start_time
        self._end_time = end_time
        self._reason = reason

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def reason(self) -> "Union[str, NotFound]":
        if isinstance(self._reason, Empty):
            raise ValueError("Field reason was not selected as part of the query")
        return self._reason


class GQLRoomPrimeTimeConfig(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        source: "Union[GQLPrimeTimeConfigSource, Empty]" = Empty(),
        sunday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        monday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        tuesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        wednesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        thursday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        friday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        saturday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._room_id = room_id
        self._room = room
        self._source = source
        self._sunday_start_time = sunday_start_time
        self._sunday_end_time = sunday_end_time
        self._monday_start_time = monday_start_time
        self._monday_end_time = monday_end_time
        self._tuesday_start_time = tuesday_start_time
        self._tuesday_end_time = tuesday_end_time
        self._wednesday_start_time = wednesday_start_time
        self._wednesday_end_time = wednesday_end_time
        self._thursday_start_time = thursday_start_time
        self._thursday_end_time = thursday_end_time
        self._friday_start_time = friday_start_time
        self._friday_end_time = friday_end_time
        self._saturday_start_time = saturday_start_time
        self._saturday_end_time = saturday_end_time
        self._sunday = sunday
        self._monday = monday
        self._tuesday = tuesday
        self._wednesday = wednesday
        self._thursday = thursday
        self._friday = friday
        self._saturday = saturday

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def source(self) -> "GQLPrimeTimeConfigSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def sunday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_start_time, Empty):
            raise ValueError("Field sunday_start_time was not selected as part of the query")
        return self._sunday_start_time

    @property
    def sunday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_end_time, Empty):
            raise ValueError("Field sunday_end_time was not selected as part of the query")
        return self._sunday_end_time

    @property
    def monday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_start_time, Empty):
            raise ValueError("Field monday_start_time was not selected as part of the query")
        return self._monday_start_time

    @property
    def monday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_end_time, Empty):
            raise ValueError("Field monday_end_time was not selected as part of the query")
        return self._monday_end_time

    @property
    def tuesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_start_time, Empty):
            raise ValueError("Field tuesday_start_time was not selected as part of the query")
        return self._tuesday_start_time

    @property
    def tuesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_end_time, Empty):
            raise ValueError("Field tuesday_end_time was not selected as part of the query")
        return self._tuesday_end_time

    @property
    def wednesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_start_time, Empty):
            raise ValueError("Field wednesday_start_time was not selected as part of the query")
        return self._wednesday_start_time

    @property
    def wednesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_end_time, Empty):
            raise ValueError("Field wednesday_end_time was not selected as part of the query")
        return self._wednesday_end_time

    @property
    def thursday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_start_time, Empty):
            raise ValueError("Field thursday_start_time was not selected as part of the query")
        return self._thursday_start_time

    @property
    def thursday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_end_time, Empty):
            raise ValueError("Field thursday_end_time was not selected as part of the query")
        return self._thursday_end_time

    @property
    def friday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_start_time, Empty):
            raise ValueError("Field friday_start_time was not selected as part of the query")
        return self._friday_start_time

    @property
    def friday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_end_time, Empty):
            raise ValueError("Field friday_end_time was not selected as part of the query")
        return self._friday_end_time

    @property
    def saturday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_start_time, Empty):
            raise ValueError("Field saturday_start_time was not selected as part of the query")
        return self._saturday_start_time

    @property
    def saturday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_end_time, Empty):
            raise ValueError("Field saturday_end_time was not selected as part of the query")
        return self._saturday_end_time

    @property
    def sunday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._sunday, Empty):
            raise ValueError("Field sunday was not selected as part of the query")
        return self._sunday

    @property
    def monday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._monday, Empty):
            raise ValueError("Field monday was not selected as part of the query")
        return self._monday

    @property
    def tuesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._tuesday, Empty):
            raise ValueError("Field tuesday was not selected as part of the query")
        return self._tuesday

    @property
    def wednesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._wednesday, Empty):
            raise ValueError("Field wednesday was not selected as part of the query")
        return self._wednesday

    @property
    def thursday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._thursday, Empty):
            raise ValueError("Field thursday was not selected as part of the query")
        return self._thursday

    @property
    def friday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._friday, Empty):
            raise ValueError("Field friday was not selected as part of the query")
        return self._friday

    @property
    def saturday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._saturday, Empty):
            raise ValueError("Field saturday was not selected as part of the query")
        return self._saturday


class GQLTimeRange(GQLClientObject):
    def __init__(
        self,
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
    ) -> None:
        self._start_time = start_time
        self._end_time = end_time

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time


class GQLRoomFirstCaseConfig(GQLClientObject):
    def __init__(
        self,
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        source: "Union[GQLFirstCaseConfigSource, Empty]" = Empty(),
        sunday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        monday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        tuesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        wednesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        thursday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        friday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        saturday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
    ) -> None:
        self._room_id = room_id
        self._room = room
        self._source = source
        self._sunday_start_time = sunday_start_time
        self._sunday_end_time = sunday_end_time
        self._monday_start_time = monday_start_time
        self._monday_end_time = monday_end_time
        self._tuesday_start_time = tuesday_start_time
        self._tuesday_end_time = tuesday_end_time
        self._wednesday_start_time = wednesday_start_time
        self._wednesday_end_time = wednesday_end_time
        self._thursday_start_time = thursday_start_time
        self._thursday_end_time = thursday_end_time
        self._friday_start_time = friday_start_time
        self._friday_end_time = friday_end_time
        self._saturday_start_time = saturday_start_time
        self._saturday_end_time = saturday_end_time
        self._sunday = sunday
        self._monday = monday
        self._tuesday = tuesday
        self._wednesday = wednesday
        self._thursday = thursday
        self._friday = friday
        self._saturday = saturday

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def source(self) -> "GQLFirstCaseConfigSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def sunday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_start_time, Empty):
            raise ValueError("Field sunday_start_time was not selected as part of the query")
        return self._sunday_start_time

    @property
    def sunday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_end_time, Empty):
            raise ValueError("Field sunday_end_time was not selected as part of the query")
        return self._sunday_end_time

    @property
    def monday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_start_time, Empty):
            raise ValueError("Field monday_start_time was not selected as part of the query")
        return self._monday_start_time

    @property
    def monday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_end_time, Empty):
            raise ValueError("Field monday_end_time was not selected as part of the query")
        return self._monday_end_time

    @property
    def tuesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_start_time, Empty):
            raise ValueError("Field tuesday_start_time was not selected as part of the query")
        return self._tuesday_start_time

    @property
    def tuesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_end_time, Empty):
            raise ValueError("Field tuesday_end_time was not selected as part of the query")
        return self._tuesday_end_time

    @property
    def wednesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_start_time, Empty):
            raise ValueError("Field wednesday_start_time was not selected as part of the query")
        return self._wednesday_start_time

    @property
    def wednesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_end_time, Empty):
            raise ValueError("Field wednesday_end_time was not selected as part of the query")
        return self._wednesday_end_time

    @property
    def thursday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_start_time, Empty):
            raise ValueError("Field thursday_start_time was not selected as part of the query")
        return self._thursday_start_time

    @property
    def thursday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_end_time, Empty):
            raise ValueError("Field thursday_end_time was not selected as part of the query")
        return self._thursday_end_time

    @property
    def friday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_start_time, Empty):
            raise ValueError("Field friday_start_time was not selected as part of the query")
        return self._friday_start_time

    @property
    def friday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_end_time, Empty):
            raise ValueError("Field friday_end_time was not selected as part of the query")
        return self._friday_end_time

    @property
    def saturday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_start_time, Empty):
            raise ValueError("Field saturday_start_time was not selected as part of the query")
        return self._saturday_start_time

    @property
    def saturday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_end_time, Empty):
            raise ValueError("Field saturday_end_time was not selected as part of the query")
        return self._saturday_end_time

    @property
    def sunday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._sunday, Empty):
            raise ValueError("Field sunday was not selected as part of the query")
        return self._sunday

    @property
    def monday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._monday, Empty):
            raise ValueError("Field monday was not selected as part of the query")
        return self._monday

    @property
    def tuesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._tuesday, Empty):
            raise ValueError("Field tuesday was not selected as part of the query")
        return self._tuesday

    @property
    def wednesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._wednesday, Empty):
            raise ValueError("Field wednesday was not selected as part of the query")
        return self._wednesday

    @property
    def thursday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._thursday, Empty):
            raise ValueError("Field thursday was not selected as part of the query")
        return self._thursday

    @property
    def friday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._friday, Empty):
            raise ValueError("Field friday was not selected as part of the query")
        return self._friday

    @property
    def saturday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._saturday, Empty):
            raise ValueError("Field saturday was not selected as part of the query")
        return self._saturday


class GQLTurnoverGoals(GQLClientObject):
    def __init__(
        self,
        goal_minutes: "Union[int, NotFound, Empty]" = Empty(),
        max_minutes: "Union[int, Empty]" = Empty(),
    ) -> None:
        self._goal_minutes = goal_minutes
        self._max_minutes = max_minutes

    @property
    def goal_minutes(self) -> "Union[int, NotFound]":
        if isinstance(self._goal_minutes, Empty):
            raise ValueError("Field goal_minutes was not selected as part of the query")
        return self._goal_minutes

    @property
    def max_minutes(self) -> "int":
        if isinstance(self._max_minutes, Empty):
            raise ValueError("Field max_minutes was not selected as part of the query")
        return self._max_minutes


class GQLSitePrimeTimeConfig(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        source: "Union[GQLSitePrimeTimeConfigSource, Empty]" = Empty(),
        sunday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        monday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        tuesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        wednesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        thursday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        friday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        saturday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._site_id = site_id
        self._site = site
        self._source = source
        self._sunday_start_time = sunday_start_time
        self._sunday_end_time = sunday_end_time
        self._monday_start_time = monday_start_time
        self._monday_end_time = monday_end_time
        self._tuesday_start_time = tuesday_start_time
        self._tuesday_end_time = tuesday_end_time
        self._wednesday_start_time = wednesday_start_time
        self._wednesday_end_time = wednesday_end_time
        self._thursday_start_time = thursday_start_time
        self._thursday_end_time = thursday_end_time
        self._friday_start_time = friday_start_time
        self._friday_end_time = friday_end_time
        self._saturday_start_time = saturday_start_time
        self._saturday_end_time = saturday_end_time
        self._sunday = sunday
        self._monday = monday
        self._tuesday = tuesday
        self._wednesday = wednesday
        self._thursday = thursday
        self._friday = friday
        self._saturday = saturday

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def source(self) -> "GQLSitePrimeTimeConfigSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def sunday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_start_time, Empty):
            raise ValueError("Field sunday_start_time was not selected as part of the query")
        return self._sunday_start_time

    @property
    def sunday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_end_time, Empty):
            raise ValueError("Field sunday_end_time was not selected as part of the query")
        return self._sunday_end_time

    @property
    def monday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_start_time, Empty):
            raise ValueError("Field monday_start_time was not selected as part of the query")
        return self._monday_start_time

    @property
    def monday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_end_time, Empty):
            raise ValueError("Field monday_end_time was not selected as part of the query")
        return self._monday_end_time

    @property
    def tuesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_start_time, Empty):
            raise ValueError("Field tuesday_start_time was not selected as part of the query")
        return self._tuesday_start_time

    @property
    def tuesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_end_time, Empty):
            raise ValueError("Field tuesday_end_time was not selected as part of the query")
        return self._tuesday_end_time

    @property
    def wednesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_start_time, Empty):
            raise ValueError("Field wednesday_start_time was not selected as part of the query")
        return self._wednesday_start_time

    @property
    def wednesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_end_time, Empty):
            raise ValueError("Field wednesday_end_time was not selected as part of the query")
        return self._wednesday_end_time

    @property
    def thursday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_start_time, Empty):
            raise ValueError("Field thursday_start_time was not selected as part of the query")
        return self._thursday_start_time

    @property
    def thursday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_end_time, Empty):
            raise ValueError("Field thursday_end_time was not selected as part of the query")
        return self._thursday_end_time

    @property
    def friday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_start_time, Empty):
            raise ValueError("Field friday_start_time was not selected as part of the query")
        return self._friday_start_time

    @property
    def friday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_end_time, Empty):
            raise ValueError("Field friday_end_time was not selected as part of the query")
        return self._friday_end_time

    @property
    def saturday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_start_time, Empty):
            raise ValueError("Field saturday_start_time was not selected as part of the query")
        return self._saturday_start_time

    @property
    def saturday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_end_time, Empty):
            raise ValueError("Field saturday_end_time was not selected as part of the query")
        return self._saturday_end_time

    @property
    def sunday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._sunday, Empty):
            raise ValueError("Field sunday was not selected as part of the query")
        return self._sunday

    @property
    def monday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._monday, Empty):
            raise ValueError("Field monday was not selected as part of the query")
        return self._monday

    @property
    def tuesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._tuesday, Empty):
            raise ValueError("Field tuesday was not selected as part of the query")
        return self._tuesday

    @property
    def wednesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._wednesday, Empty):
            raise ValueError("Field wednesday was not selected as part of the query")
        return self._wednesday

    @property
    def thursday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._thursday, Empty):
            raise ValueError("Field thursday was not selected as part of the query")
        return self._thursday

    @property
    def friday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._friday, Empty):
            raise ValueError("Field friday was not selected as part of the query")
        return self._friday

    @property
    def saturday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._saturday, Empty):
            raise ValueError("Field saturday was not selected as part of the query")
        return self._saturday


class GQLSiteFirstCaseConfig(GQLClientObject):
    def __init__(
        self,
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        source: "Union[GQLSiteFirstCaseConfigSource, Empty]" = Empty(),
        sunday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        monday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        tuesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        wednesday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        thursday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        friday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_start_time: "Union[datetime, NotFound, Empty]" = Empty(),
        saturday_end_time: "Union[datetime, NotFound, Empty]" = Empty(),
        sunday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        monday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        tuesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        wednesday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        thursday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        friday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
        saturday: "Union[GQLTimeRange, NotFound, Empty]" = Empty(),
    ) -> None:
        self._site_id = site_id
        self._site = site
        self._source = source
        self._sunday_start_time = sunday_start_time
        self._sunday_end_time = sunday_end_time
        self._monday_start_time = monday_start_time
        self._monday_end_time = monday_end_time
        self._tuesday_start_time = tuesday_start_time
        self._tuesday_end_time = tuesday_end_time
        self._wednesday_start_time = wednesday_start_time
        self._wednesday_end_time = wednesday_end_time
        self._thursday_start_time = thursday_start_time
        self._thursday_end_time = thursday_end_time
        self._friday_start_time = friday_start_time
        self._friday_end_time = friday_end_time
        self._saturday_start_time = saturday_start_time
        self._saturday_end_time = saturday_end_time
        self._sunday = sunday
        self._monday = monday
        self._tuesday = tuesday
        self._wednesday = wednesday
        self._thursday = thursday
        self._friday = friday
        self._saturday = saturday

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def source(self) -> "GQLSiteFirstCaseConfigSource":
        if isinstance(self._source, Empty):
            raise ValueError("Field source was not selected as part of the query")
        return self._source

    @property
    def sunday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_start_time, Empty):
            raise ValueError("Field sunday_start_time was not selected as part of the query")
        return self._sunday_start_time

    @property
    def sunday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._sunday_end_time, Empty):
            raise ValueError("Field sunday_end_time was not selected as part of the query")
        return self._sunday_end_time

    @property
    def monday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_start_time, Empty):
            raise ValueError("Field monday_start_time was not selected as part of the query")
        return self._monday_start_time

    @property
    def monday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._monday_end_time, Empty):
            raise ValueError("Field monday_end_time was not selected as part of the query")
        return self._monday_end_time

    @property
    def tuesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_start_time, Empty):
            raise ValueError("Field tuesday_start_time was not selected as part of the query")
        return self._tuesday_start_time

    @property
    def tuesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._tuesday_end_time, Empty):
            raise ValueError("Field tuesday_end_time was not selected as part of the query")
        return self._tuesday_end_time

    @property
    def wednesday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_start_time, Empty):
            raise ValueError("Field wednesday_start_time was not selected as part of the query")
        return self._wednesday_start_time

    @property
    def wednesday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._wednesday_end_time, Empty):
            raise ValueError("Field wednesday_end_time was not selected as part of the query")
        return self._wednesday_end_time

    @property
    def thursday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_start_time, Empty):
            raise ValueError("Field thursday_start_time was not selected as part of the query")
        return self._thursday_start_time

    @property
    def thursday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._thursday_end_time, Empty):
            raise ValueError("Field thursday_end_time was not selected as part of the query")
        return self._thursday_end_time

    @property
    def friday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_start_time, Empty):
            raise ValueError("Field friday_start_time was not selected as part of the query")
        return self._friday_start_time

    @property
    def friday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._friday_end_time, Empty):
            raise ValueError("Field friday_end_time was not selected as part of the query")
        return self._friday_end_time

    @property
    def saturday_start_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_start_time, Empty):
            raise ValueError("Field saturday_start_time was not selected as part of the query")
        return self._saturday_start_time

    @property
    def saturday_end_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._saturday_end_time, Empty):
            raise ValueError("Field saturday_end_time was not selected as part of the query")
        return self._saturday_end_time

    @property
    def sunday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._sunday, Empty):
            raise ValueError("Field sunday was not selected as part of the query")
        return self._sunday

    @property
    def monday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._monday, Empty):
            raise ValueError("Field monday was not selected as part of the query")
        return self._monday

    @property
    def tuesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._tuesday, Empty):
            raise ValueError("Field tuesday was not selected as part of the query")
        return self._tuesday

    @property
    def wednesday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._wednesday, Empty):
            raise ValueError("Field wednesday was not selected as part of the query")
        return self._wednesday

    @property
    def thursday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._thursday, Empty):
            raise ValueError("Field thursday was not selected as part of the query")
        return self._thursday

    @property
    def friday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._friday, Empty):
            raise ValueError("Field friday was not selected as part of the query")
        return self._friday

    @property
    def saturday(self) -> "Union[GQLTimeRange, NotFound]":
        if isinstance(self._saturday, Empty):
            raise ValueError("Field saturday was not selected as part of the query")
        return self._saturday


class GQLSiteCapacityConstraint(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        day_of_week: "Union[int, Empty]" = Empty(),
        count: "Union[int, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._site_id = site_id
        self._site = site
        self._day_of_week = day_of_week
        self._count = count
        self._start_time = start_time

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def day_of_week(self) -> "int":
        if isinstance(self._day_of_week, Empty):
            raise ValueError("Field day_of_week was not selected as part of the query")
        return self._day_of_week

    @property
    def count(self) -> "int":
        if isinstance(self._count, Empty):
            raise ValueError("Field count was not selected as part of the query")
        return self._count

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time


class GQLStaffingNeedsRatio(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        ratio: "Union[float, NotFound, Empty]" = Empty(),
        staff_role: "Union[GQLStaffRole, Empty]" = Empty(),
        set_by_user_id: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._site_id = site_id
        self._ratio = ratio
        self._staff_role = staff_role
        self._set_by_user_id = set_by_user_id

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def ratio(self) -> "Union[float, NotFound]":
        if isinstance(self._ratio, Empty):
            raise ValueError("Field ratio was not selected as part of the query")
        return self._ratio

    @property
    def staff_role(self) -> "GQLStaffRole":
        if isinstance(self._staff_role, Empty):
            raise ValueError("Field staff_role was not selected as part of the query")
        return self._staff_role

    @property
    def set_by_user_id(self) -> "str":
        if isinstance(self._set_by_user_id, Empty):
            raise ValueError("Field set_by_user_id was not selected as part of the query")
        return self._set_by_user_id


class GQLStaffRole(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name


class GQLSiteClosureConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLSiteClosureEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLSiteClosureEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLSiteClosureEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLSiteClosure, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLSiteClosure":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLSiteClosure(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        closure_date: "Union[datetime, Empty]" = Empty(),
        reason: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._site_id = site_id
        self._site = site
        self._closure_date = closure_date
        self._reason = reason

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def closure_date(self) -> "datetime":
        if isinstance(self._closure_date, Empty):
            raise ValueError("Field closure_date was not selected as part of the query")
        return self._closure_date

    @property
    def reason(self) -> "str":
        if isinstance(self._reason, Empty):
            raise ValueError("Field reason was not selected as part of the query")
        return self._reason


class GQLCaseLabelCategory(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        fields: "Union[list[GQLCaseLabelField], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._fields = fields

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def fields(self) -> "list[GQLCaseLabelField]":
        if isinstance(self._fields, Empty):
            raise ValueError("Field fields was not selected as part of the query")
        return self._fields


class GQLCaseLabelField(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        type: "Union[GQLCaseLabelFieldType, Empty]" = Empty(),
        ordinal: "Union[int, Empty]" = Empty(),
        options: "Union[list[GQLCaseLabelFieldOption], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._type = type
        self._ordinal = ordinal
        self._options = options

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def type(self) -> "GQLCaseLabelFieldType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def ordinal(self) -> "int":
        if isinstance(self._ordinal, Empty):
            raise ValueError("Field ordinal was not selected as part of the query")
        return self._ordinal

    @property
    def options(self) -> "list[GQLCaseLabelFieldOption]":
        if isinstance(self._options, Empty):
            raise ValueError("Field options was not selected as part of the query")
        return self._options


class GQLCaseLabelFieldOption(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        field_id: "Union[str, Empty]" = Empty(),
        color: "Union[str, Empty]" = Empty(),
        abbreviation: "Union[str, Empty]" = Empty(),
        value: "Union[str, Empty]" = Empty(),
        boolean_value: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._field_id = field_id
        self._color = color
        self._abbreviation = abbreviation
        self._value = value
        self._boolean_value = boolean_value

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def field_id(self) -> "str":
        if isinstance(self._field_id, Empty):
            raise ValueError("Field field_id was not selected as part of the query")
        return self._field_id

    @property
    def color(self) -> "str":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color

    @property
    def abbreviation(self) -> "str":
        if isinstance(self._abbreviation, Empty):
            raise ValueError("Field abbreviation was not selected as part of the query")
        return self._abbreviation

    @property
    def value(self) -> "str":
        if isinstance(self._value, Empty):
            raise ValueError("Field value was not selected as part of the query")
        return self._value

    @property
    def boolean_value(self) -> "Union[bool, NotFound]":
        if isinstance(self._boolean_value, Empty):
            raise ValueError("Field boolean_value was not selected as part of the query")
        return self._boolean_value


class GQLUserConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLUserEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLUserEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLUserEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLUser, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLUser":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLUserUiPermissions(GQLClientObject):
    def __init__(
        self,
        big_board_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        big_board_write_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        case_duration_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        case_staff_plan_read_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        case_staff_plan_write_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        contact_information_read_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        contact_information_write_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_create_measurement_periods_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_highlights_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_insights_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_live_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_live_from_schedule_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_schedule_edit_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_schedule_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_staff_management_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        dashboard_terminal_cleans_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        available_time_email_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        notification_read_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        notification_write_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        read_any_asset_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        room_write_enabled: "Union[bool, NotFound, Empty]" = Empty(),
        room_write_configuration_enabled: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._big_board_enabled = big_board_enabled
        self._big_board_write_enabled = big_board_write_enabled
        self._case_duration_enabled = case_duration_enabled
        self._case_staff_plan_read_enabled = case_staff_plan_read_enabled
        self._case_staff_plan_write_enabled = case_staff_plan_write_enabled
        self._contact_information_read_enabled = contact_information_read_enabled
        self._contact_information_write_enabled = contact_information_write_enabled
        self._dashboard_create_measurement_periods_enabled = (
            dashboard_create_measurement_periods_enabled
        )
        self._dashboard_highlights_enabled = dashboard_highlights_enabled
        self._dashboard_insights_enabled = dashboard_insights_enabled
        self._dashboard_live_enabled = dashboard_live_enabled
        self._dashboard_live_from_schedule_enabled = dashboard_live_from_schedule_enabled
        self._dashboard_schedule_edit_enabled = dashboard_schedule_edit_enabled
        self._dashboard_schedule_enabled = dashboard_schedule_enabled
        self._dashboard_staff_management_enabled = dashboard_staff_management_enabled
        self._dashboard_terminal_cleans_enabled = dashboard_terminal_cleans_enabled
        self._available_time_email_enabled = available_time_email_enabled
        self._notification_read_enabled = notification_read_enabled
        self._notification_write_enabled = notification_write_enabled
        self._read_any_asset_enabled = read_any_asset_enabled
        self._room_write_enabled = room_write_enabled
        self._room_write_configuration_enabled = room_write_configuration_enabled

    @property
    def big_board_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._big_board_enabled, Empty):
            raise ValueError("Field big_board_enabled was not selected as part of the query")
        return self._big_board_enabled

    @property
    def big_board_write_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._big_board_write_enabled, Empty):
            raise ValueError("Field big_board_write_enabled was not selected as part of the query")
        return self._big_board_write_enabled

    @property
    def case_duration_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._case_duration_enabled, Empty):
            raise ValueError("Field case_duration_enabled was not selected as part of the query")
        return self._case_duration_enabled

    @property
    def case_staff_plan_read_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._case_staff_plan_read_enabled, Empty):
            raise ValueError(
                "Field case_staff_plan_read_enabled was not selected as part of the query"
            )
        return self._case_staff_plan_read_enabled

    @property
    def case_staff_plan_write_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._case_staff_plan_write_enabled, Empty):
            raise ValueError(
                "Field case_staff_plan_write_enabled was not selected as part of the query"
            )
        return self._case_staff_plan_write_enabled

    @property
    def contact_information_read_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._contact_information_read_enabled, Empty):
            raise ValueError(
                "Field contact_information_read_enabled was not selected as part of the query"
            )
        return self._contact_information_read_enabled

    @property
    def contact_information_write_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._contact_information_write_enabled, Empty):
            raise ValueError(
                "Field contact_information_write_enabled was not selected as part of the query"
            )
        return self._contact_information_write_enabled

    @property
    def dashboard_create_measurement_periods_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_create_measurement_periods_enabled, Empty):
            raise ValueError(
                "Field dashboard_create_measurement_periods_enabled was not selected as part of the query"
            )
        return self._dashboard_create_measurement_periods_enabled

    @property
    def dashboard_highlights_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_highlights_enabled, Empty):
            raise ValueError(
                "Field dashboard_highlights_enabled was not selected as part of the query"
            )
        return self._dashboard_highlights_enabled

    @property
    def dashboard_insights_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_insights_enabled, Empty):
            raise ValueError(
                "Field dashboard_insights_enabled was not selected as part of the query"
            )
        return self._dashboard_insights_enabled

    @property
    def dashboard_live_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_live_enabled, Empty):
            raise ValueError("Field dashboard_live_enabled was not selected as part of the query")
        return self._dashboard_live_enabled

    @property
    def dashboard_live_from_schedule_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_live_from_schedule_enabled, Empty):
            raise ValueError(
                "Field dashboard_live_from_schedule_enabled was not selected as part of the query"
            )
        return self._dashboard_live_from_schedule_enabled

    @property
    def dashboard_schedule_edit_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_schedule_edit_enabled, Empty):
            raise ValueError(
                "Field dashboard_schedule_edit_enabled was not selected as part of the query"
            )
        return self._dashboard_schedule_edit_enabled

    @property
    def dashboard_schedule_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_schedule_enabled, Empty):
            raise ValueError(
                "Field dashboard_schedule_enabled was not selected as part of the query"
            )
        return self._dashboard_schedule_enabled

    @property
    def dashboard_staff_management_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_staff_management_enabled, Empty):
            raise ValueError(
                "Field dashboard_staff_management_enabled was not selected as part of the query"
            )
        return self._dashboard_staff_management_enabled

    @property
    def dashboard_terminal_cleans_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._dashboard_terminal_cleans_enabled, Empty):
            raise ValueError(
                "Field dashboard_terminal_cleans_enabled was not selected as part of the query"
            )
        return self._dashboard_terminal_cleans_enabled

    @property
    def available_time_email_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._available_time_email_enabled, Empty):
            raise ValueError(
                "Field available_time_email_enabled was not selected as part of the query"
            )
        return self._available_time_email_enabled

    @property
    def notification_read_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._notification_read_enabled, Empty):
            raise ValueError(
                "Field notification_read_enabled was not selected as part of the query"
            )
        return self._notification_read_enabled

    @property
    def notification_write_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._notification_write_enabled, Empty):
            raise ValueError(
                "Field notification_write_enabled was not selected as part of the query"
            )
        return self._notification_write_enabled

    @property
    def read_any_asset_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._read_any_asset_enabled, Empty):
            raise ValueError("Field read_any_asset_enabled was not selected as part of the query")
        return self._read_any_asset_enabled

    @property
    def room_write_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._room_write_enabled, Empty):
            raise ValueError("Field room_write_enabled was not selected as part of the query")
        return self._room_write_enabled

    @property
    def room_write_configuration_enabled(self) -> "Union[bool, NotFound]":
        if isinstance(self._room_write_configuration_enabled, Empty):
            raise ValueError(
                "Field room_write_configuration_enabled was not selected as part of the query"
            )
        return self._room_write_configuration_enabled


class GQLRoomTagConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLRoomTagEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLRoomTagEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLRoomTagEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLRoomTag, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLRoomTag":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCluster(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        enable_audio: "Union[bool, Empty]" = Empty(),
        sites: "Union[GQLSiteConnection, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._enable_audio = enable_audio
        self._sites = sites

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def enable_audio(self) -> "bool":
        if isinstance(self._enable_audio, Empty):
            raise ValueError("Field enable_audio was not selected as part of the query")
        return self._enable_audio

    @property
    def sites(self) -> "GQLSiteConnection":
        if isinstance(self._sites, Empty):
            raise ValueError("Field sites was not selected as part of the query")
        return self._sites


class GQLClusterConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLClusterEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLClusterEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLClusterEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCluster, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCluster":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLEventLabelOption(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name


class GQLEventTypeConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLEventTypeEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLEventTypeEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLEventTypeEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLEventType, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLEventType":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLHighlight(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        description: "Union[str, Empty]" = Empty(),
        organization_id: "Union[str, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        category: "Union[str, Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
        feedback: "Union[GQLHighlightFeedbackConnection, Empty]" = Empty(),
        my_feedback: "Union[GQLHighlightFeedback, NotFound, Empty]" = Empty(),
        users: "Union[GQLUserConnection, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        has_video_available: "Union[bool, Empty]" = Empty(),
        duration: "Union[timedelta, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._description = description
        self._organization_id = organization_id
        self._site_id = site_id
        self._room_id = room_id
        self._start_time = start_time
        self._end_time = end_time
        self._category = category
        self._archived_time = archived_time
        self._feedback = feedback
        self._my_feedback = my_feedback
        self._users = users
        self._organization = organization
        self._site = site
        self._room = room
        self._has_video_available = has_video_available
        self._duration = duration

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def description(self) -> "str":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description

    @property
    def organization_id(self) -> "str":
        if isinstance(self._organization_id, Empty):
            raise ValueError("Field organization_id was not selected as part of the query")
        return self._organization_id

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def category(self) -> "str":
        if isinstance(self._category, Empty):
            raise ValueError("Field category was not selected as part of the query")
        return self._category

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time

    @property
    def feedback(self) -> "GQLHighlightFeedbackConnection":
        if isinstance(self._feedback, Empty):
            raise ValueError("Field feedback was not selected as part of the query")
        return self._feedback

    @property
    def my_feedback(self) -> "Union[GQLHighlightFeedback, NotFound]":
        if isinstance(self._my_feedback, Empty):
            raise ValueError("Field my_feedback was not selected as part of the query")
        return self._my_feedback

    @property
    def users(self) -> "GQLUserConnection":
        if isinstance(self._users, Empty):
            raise ValueError("Field users was not selected as part of the query")
        return self._users

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def has_video_available(self) -> "bool":
        if isinstance(self._has_video_available, Empty):
            raise ValueError("Field has_video_available was not selected as part of the query")
        return self._has_video_available

    @property
    def duration(self) -> "Union[timedelta, NotFound]":
        if isinstance(self._duration, Empty):
            raise ValueError("Field duration was not selected as part of the query")
        return self._duration


class GQLHighlightFeedbackConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLHighlightFeedbackEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLHighlightFeedbackEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLHighlightFeedbackEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLHighlightFeedback, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLHighlightFeedback":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLHighlightFeedback(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        comment: "Union[str, NotFound, Empty]" = Empty(),
        rating: "Union[int, NotFound, Empty]" = Empty(),
        submitted_date: "Union[datetime, NotFound, Empty]" = Empty(),
        highlight_id: "Union[str, NotFound, Empty]" = Empty(),
        highlight: "Union[GQLHighlight, Empty]" = Empty(),
        user_id: "Union[str, NotFound, Empty]" = Empty(),
        user: "Union[GQLUser, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._comment = comment
        self._rating = rating
        self._submitted_date = submitted_date
        self._highlight_id = highlight_id
        self._highlight = highlight
        self._user_id = user_id
        self._user = user

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def comment(self) -> "Union[str, NotFound]":
        if isinstance(self._comment, Empty):
            raise ValueError("Field comment was not selected as part of the query")
        return self._comment

    @property
    def rating(self) -> "Union[int, NotFound]":
        if isinstance(self._rating, Empty):
            raise ValueError("Field rating was not selected as part of the query")
        return self._rating

    @property
    def submitted_date(self) -> "Union[datetime, NotFound]":
        if isinstance(self._submitted_date, Empty):
            raise ValueError("Field submitted_date was not selected as part of the query")
        return self._submitted_date

    @property
    def highlight_id(self) -> "Union[str, NotFound]":
        if isinstance(self._highlight_id, Empty):
            raise ValueError("Field highlight_id was not selected as part of the query")
        return self._highlight_id

    @property
    def highlight(self) -> "GQLHighlight":
        if isinstance(self._highlight, Empty):
            raise ValueError("Field highlight was not selected as part of the query")
        return self._highlight

    @property
    def user_id(self) -> "Union[str, NotFound]":
        if isinstance(self._user_id, Empty):
            raise ValueError("Field user_id was not selected as part of the query")
        return self._user_id

    @property
    def user(self) -> "GQLUser":
        if isinstance(self._user, Empty):
            raise ValueError("Field user was not selected as part of the query")
        return self._user


class GQLHighlightConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLHighlightEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLHighlightEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLHighlightEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLHighlight, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLHighlight":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLAnnotationTask(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        status: "Union[GQLTaskStatus, Empty]" = Empty(),
        cancelled_reason: "Union[GQLCancelledReason, NotFound, Empty]" = Empty(),
        updated_time: "Union[datetime, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        site_id: "Union[str, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        annotator_user_id: "Union[str, NotFound, Empty]" = Empty(),
        annotator: "Union[GQLUser, NotFound, Empty]" = Empty(),
        reviewer_user_id: "Union[str, NotFound, Empty]" = Empty(),
        reviewer: "Union[GQLUser, NotFound, Empty]" = Empty(),
        updated_by_user_id: "Union[str, NotFound, Empty]" = Empty(),
        updated_by_user: "Union[GQLUser, NotFound, Empty]" = Empty(),
        type_id: "Union[str, Empty]" = Empty(),
        type: "Union[GQLAnnotationTaskType, Empty]" = Empty(),
        events: "Union[list[GQLEvent], Empty]" = Empty(),
        context_events: "Union[list[GQLEvent], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._start_time = start_time
        self._end_time = end_time
        self._status = status
        self._cancelled_reason = cancelled_reason
        self._updated_time = updated_time
        self._org_id = org_id
        self._organization = organization
        self._site_id = site_id
        self._site = site
        self._room_id = room_id
        self._room = room
        self._annotator_user_id = annotator_user_id
        self._annotator = annotator
        self._reviewer_user_id = reviewer_user_id
        self._reviewer = reviewer
        self._updated_by_user_id = updated_by_user_id
        self._updated_by_user = updated_by_user
        self._type_id = type_id
        self._type = type
        self._events = events
        self._context_events = context_events

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def status(self) -> "GQLTaskStatus":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status

    @property
    def cancelled_reason(self) -> "Union[GQLCancelledReason, NotFound]":
        if isinstance(self._cancelled_reason, Empty):
            raise ValueError("Field cancelled_reason was not selected as part of the query")
        return self._cancelled_reason

    @property
    def updated_time(self) -> "datetime":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def site_id(self) -> "str":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def annotator_user_id(self) -> "Union[str, NotFound]":
        if isinstance(self._annotator_user_id, Empty):
            raise ValueError("Field annotator_user_id was not selected as part of the query")
        return self._annotator_user_id

    @property
    def annotator(self) -> "Union[GQLUser, NotFound]":
        if isinstance(self._annotator, Empty):
            raise ValueError("Field annotator was not selected as part of the query")
        return self._annotator

    @property
    def reviewer_user_id(self) -> "Union[str, NotFound]":
        if isinstance(self._reviewer_user_id, Empty):
            raise ValueError("Field reviewer_user_id was not selected as part of the query")
        return self._reviewer_user_id

    @property
    def reviewer(self) -> "Union[GQLUser, NotFound]":
        if isinstance(self._reviewer, Empty):
            raise ValueError("Field reviewer was not selected as part of the query")
        return self._reviewer

    @property
    def updated_by_user_id(self) -> "Union[str, NotFound]":
        if isinstance(self._updated_by_user_id, Empty):
            raise ValueError("Field updated_by_user_id was not selected as part of the query")
        return self._updated_by_user_id

    @property
    def updated_by_user(self) -> "Union[GQLUser, NotFound]":
        if isinstance(self._updated_by_user, Empty):
            raise ValueError("Field updated_by_user was not selected as part of the query")
        return self._updated_by_user

    @property
    def type_id(self) -> "str":
        if isinstance(self._type_id, Empty):
            raise ValueError("Field type_id was not selected as part of the query")
        return self._type_id

    @property
    def type(self) -> "GQLAnnotationTaskType":
        if isinstance(self._type, Empty):
            raise ValueError("Field type was not selected as part of the query")
        return self._type

    @property
    def events(self) -> "list[GQLEvent]":
        if isinstance(self._events, Empty):
            raise ValueError("Field events was not selected as part of the query")
        return self._events

    @property
    def context_events(self) -> "list[GQLEvent]":
        if isinstance(self._context_events, Empty):
            raise ValueError("Field context_events was not selected as part of the query")
        return self._context_events


class GQLAnnotationTaskType(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        description: "Union[str, NotFound, Empty]" = Empty(),
        event_types: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        context_event_types: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        archived_time: "Union[datetime, NotFound, Empty]" = Empty(),
        schedules: "Union[Union[list[GQLAnnotationTaskSchedule], NotFound], Empty]" = Empty(),
        annotator_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        provisional_annotator_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        reviewer_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
        priority: "Union[int, Empty]" = Empty(),
        detect_idle: "Union[bool, Empty]" = Empty(),
        allow_skipping_review: "Union[bool, Empty]" = Empty(),
        optimize_tasks: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._description = description
        self._event_types = event_types
        self._context_event_types = context_event_types
        self._archived_time = archived_time
        self._schedules = schedules
        self._annotator_ids = annotator_ids
        self._provisional_annotator_ids = provisional_annotator_ids
        self._reviewer_ids = reviewer_ids
        self._priority = priority
        self._detect_idle = detect_idle
        self._allow_skipping_review = allow_skipping_review
        self._optimize_tasks = optimize_tasks

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def description(self) -> "Union[str, NotFound]":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description

    @property
    def event_types(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._event_types, Empty):
            raise ValueError("Field event_types was not selected as part of the query")
        return self._event_types

    @property
    def context_event_types(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._context_event_types, Empty):
            raise ValueError("Field context_event_types was not selected as part of the query")
        return self._context_event_types

    @property
    def archived_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._archived_time, Empty):
            raise ValueError("Field archived_time was not selected as part of the query")
        return self._archived_time

    @property
    def schedules(self) -> "Union[Union[list[GQLAnnotationTaskSchedule], NotFound]]":
        if isinstance(self._schedules, Empty):
            raise ValueError("Field schedules was not selected as part of the query")
        return self._schedules

    @property
    def annotator_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._annotator_ids, Empty):
            raise ValueError("Field annotator_ids was not selected as part of the query")
        return self._annotator_ids

    @property
    def provisional_annotator_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._provisional_annotator_ids, Empty):
            raise ValueError(
                "Field provisional_annotator_ids was not selected as part of the query"
            )
        return self._provisional_annotator_ids

    @property
    def reviewer_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._reviewer_ids, Empty):
            raise ValueError("Field reviewer_ids was not selected as part of the query")
        return self._reviewer_ids

    @property
    def priority(self) -> "int":
        if isinstance(self._priority, Empty):
            raise ValueError("Field priority was not selected as part of the query")
        return self._priority

    @property
    def detect_idle(self) -> "bool":
        if isinstance(self._detect_idle, Empty):
            raise ValueError("Field detect_idle was not selected as part of the query")
        return self._detect_idle

    @property
    def allow_skipping_review(self) -> "bool":
        if isinstance(self._allow_skipping_review, Empty):
            raise ValueError("Field allow_skipping_review was not selected as part of the query")
        return self._allow_skipping_review

    @property
    def optimize_tasks(self) -> "bool":
        if isinstance(self._optimize_tasks, Empty):
            raise ValueError("Field optimize_tasks was not selected as part of the query")
        return self._optimize_tasks


class GQLAnnotationTaskSchedule(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        interval: "Union[int, Empty]" = Empty(),
        sites: "Union[Union[list[GQLSite], NotFound], Empty]" = Empty(),
        rooms: "Union[Union[list[GQLRoom], NotFound], Empty]" = Empty(),
        annotation_task_type_id: "Union[str, Empty]" = Empty(),
        annotation_task_type: "Union[GQLAnnotationTaskType, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._start_time = start_time
        self._interval = interval
        self._sites = sites
        self._rooms = rooms
        self._annotation_task_type_id = annotation_task_type_id
        self._annotation_task_type = annotation_task_type

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def interval(self) -> "int":
        if isinstance(self._interval, Empty):
            raise ValueError("Field interval was not selected as part of the query")
        return self._interval

    @property
    def sites(self) -> "Union[Union[list[GQLSite], NotFound]]":
        if isinstance(self._sites, Empty):
            raise ValueError("Field sites was not selected as part of the query")
        return self._sites

    @property
    def rooms(self) -> "Union[Union[list[GQLRoom], NotFound]]":
        if isinstance(self._rooms, Empty):
            raise ValueError("Field rooms was not selected as part of the query")
        return self._rooms

    @property
    def annotation_task_type_id(self) -> "str":
        if isinstance(self._annotation_task_type_id, Empty):
            raise ValueError("Field annotation_task_type_id was not selected as part of the query")
        return self._annotation_task_type_id

    @property
    def annotation_task_type(self) -> "GQLAnnotationTaskType":
        if isinstance(self._annotation_task_type, Empty):
            raise ValueError("Field annotation_task_type was not selected as part of the query")
        return self._annotation_task_type


class GQLAnnotationTaskConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLAnnotationTaskEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
        counts: "Union[GQLAnnotationTasksCount, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records
        self._counts = counts

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLAnnotationTaskEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records

    @property
    def counts(self) -> "Union[GQLAnnotationTasksCount, NotFound]":
        if isinstance(self._counts, Empty):
            raise ValueError("Field counts was not selected as part of the query")
        return self._counts


class GQLAnnotationTaskEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLAnnotationTask, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLAnnotationTask":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLAnnotationTasksCount(GQLClientObject):
    def __init__(
        self,
        org_id: "Union[list[GQLFieldCount], Empty]" = Empty(),
        site_id: "Union[list[GQLFieldCount], Empty]" = Empty(),
        room_id: "Union[list[GQLFieldCount], Empty]" = Empty(),
        annotator_user_id: "Union[list[GQLFieldCount], Empty]" = Empty(),
        reviewer_user_id: "Union[list[GQLFieldCount], Empty]" = Empty(),
        status: "Union[list[GQLFieldCount], Empty]" = Empty(),
    ) -> None:
        self._org_id = org_id
        self._site_id = site_id
        self._room_id = room_id
        self._annotator_user_id = annotator_user_id
        self._reviewer_user_id = reviewer_user_id
        self._status = status

    @property
    def org_id(self) -> "list[GQLFieldCount]":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def site_id(self) -> "list[GQLFieldCount]":
        if isinstance(self._site_id, Empty):
            raise ValueError("Field site_id was not selected as part of the query")
        return self._site_id

    @property
    def room_id(self) -> "list[GQLFieldCount]":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def annotator_user_id(self) -> "list[GQLFieldCount]":
        if isinstance(self._annotator_user_id, Empty):
            raise ValueError("Field annotator_user_id was not selected as part of the query")
        return self._annotator_user_id

    @property
    def reviewer_user_id(self) -> "list[GQLFieldCount]":
        if isinstance(self._reviewer_user_id, Empty):
            raise ValueError("Field reviewer_user_id was not selected as part of the query")
        return self._reviewer_user_id

    @property
    def status(self) -> "list[GQLFieldCount]":
        if isinstance(self._status, Empty):
            raise ValueError("Field status was not selected as part of the query")
        return self._status


class GQLFieldCount(GQLClientObject):
    def __init__(
        self,
        parent_id: "Union[str, NotFound, Empty]" = Empty(),
        count: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._parent_id = parent_id
        self._count = count

    @property
    def parent_id(self) -> "Union[str, NotFound]":
        if isinstance(self._parent_id, Empty):
            raise ValueError("Field parent_id was not selected as part of the query")
        return self._parent_id

    @property
    def count(self) -> "Union[int, NotFound]":
        if isinstance(self._count, Empty):
            raise ValueError("Field count was not selected as part of the query")
        return self._count


class GQLPhaseConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLPhaseEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLPhaseEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLPhaseEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLPhase, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLPhase":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLPhaseTypeConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLPhaseTypeEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLPhaseTypeEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLPhaseTypeEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLPhaseTypeRecord, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLPhaseTypeRecord":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLObjectMetrics(GQLClientObject):
    def __init__(
        self,
        count_occupancy_per_bucket: "Union[Union[list[GQLMetricBucketFloat], NotFound], Empty]" = Empty(),
        count_occupancy_and_outage_per_bucket: "Union[Union[list[GQLMultiMetricBucketFloat], NotFound], Empty]" = Empty(),
    ) -> None:
        self._count_occupancy_per_bucket = count_occupancy_per_bucket
        self._count_occupancy_and_outage_per_bucket = count_occupancy_and_outage_per_bucket

    @property
    def count_occupancy_per_bucket(self) -> "Union[Union[list[GQLMetricBucketFloat], NotFound]]":
        if isinstance(self._count_occupancy_per_bucket, Empty):
            raise ValueError(
                "Field count_occupancy_per_bucket was not selected as part of the query"
            )
        return self._count_occupancy_per_bucket

    @property
    def count_occupancy_and_outage_per_bucket(
        self,
    ) -> "Union[Union[list[GQLMultiMetricBucketFloat], NotFound]]":
        if isinstance(self._count_occupancy_and_outage_per_bucket, Empty):
            raise ValueError(
                "Field count_occupancy_and_outage_per_bucket was not selected as part of the query"
            )
        return self._count_occupancy_and_outage_per_bucket


class GQLMetricBucketFloat(GQLClientObject):
    def __init__(
        self,
        date: "Union[datetime, Empty]" = Empty(),
        value: "Union[float, Empty]" = Empty(),
        bucket_size: "Union[timedelta, Empty]" = Empty(),
    ) -> None:
        self._date = date
        self._value = value
        self._bucket_size = bucket_size

    @property
    def date(self) -> "datetime":
        if isinstance(self._date, Empty):
            raise ValueError("Field date was not selected as part of the query")
        return self._date

    @property
    def value(self) -> "float":
        if isinstance(self._value, Empty):
            raise ValueError("Field value was not selected as part of the query")
        return self._value

    @property
    def bucket_size(self) -> "timedelta":
        if isinstance(self._bucket_size, Empty):
            raise ValueError("Field bucket_size was not selected as part of the query")
        return self._bucket_size


class GQLMultiMetricBucketFloat(GQLClientObject):
    def __init__(
        self,
        date: "Union[datetime, Empty]" = Empty(),
        bucket_size: "Union[timedelta, Empty]" = Empty(),
        metrics: "Union[list[GQLMetricFloat], Empty]" = Empty(),
    ) -> None:
        self._date = date
        self._bucket_size = bucket_size
        self._metrics = metrics

    @property
    def date(self) -> "datetime":
        if isinstance(self._date, Empty):
            raise ValueError("Field date was not selected as part of the query")
        return self._date

    @property
    def bucket_size(self) -> "timedelta":
        if isinstance(self._bucket_size, Empty):
            raise ValueError("Field bucket_size was not selected as part of the query")
        return self._bucket_size

    @property
    def metrics(self) -> "list[GQLMetricFloat]":
        if isinstance(self._metrics, Empty):
            raise ValueError("Field metrics was not selected as part of the query")
        return self._metrics


class GQLMetricFloat(GQLClientObject):
    def __init__(
        self,
        metric: "Union[str, Empty]" = Empty(),
        value: "Union[float, Empty]" = Empty(),
    ) -> None:
        self._metric = metric
        self._value = value

    @property
    def metric(self) -> "str":
        if isinstance(self._metric, Empty):
            raise ValueError("Field metric was not selected as part of the query")
        return self._metric

    @property
    def value(self) -> "float":
        if isinstance(self._value, Empty):
            raise ValueError("Field value was not selected as part of the query")
        return self._value


class GQLScheduledCaseConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLScheduledCaseEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLScheduledCaseEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLScheduledCaseEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLScheduledCase, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLScheduledCase":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseEhrMessageConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseEhrMessageEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseEhrMessageEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseEhrMessageEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseEhrMessage, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseEhrMessage":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseEhrMessage(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        message: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        event_type: "Union[str, NotFound, Empty]" = Empty(),
        event_time: "Union[datetime, NotFound, Empty]" = Empty(),
        scheduled_case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._message = message
        self._organization = organization
        self._event_type = event_type
        self._event_time = event_time
        self._scheduled_case = scheduled_case

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def message(self) -> "str":
        if isinstance(self._message, Empty):
            raise ValueError("Field message was not selected as part of the query")
        return self._message

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def event_type(self) -> "Union[str, NotFound]":
        if isinstance(self._event_type, Empty):
            raise ValueError("Field event_type was not selected as part of the query")
        return self._event_type

    @property
    def event_time(self) -> "Union[datetime, NotFound]":
        if isinstance(self._event_time, Empty):
            raise ValueError("Field event_time was not selected as part of the query")
        return self._event_time

    @property
    def scheduled_case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._scheduled_case, Empty):
            raise ValueError("Field scheduled_case was not selected as part of the query")
        return self._scheduled_case


class GQLCameraLatestImageConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[Union[list[GQLCameraLatestImageEdge], NotFound], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "Union[Union[list[GQLCameraLatestImageEdge], NotFound]]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCameraLatestImageEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCameraLatestImage, NotFound, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "Union[GQLCameraLatestImage, NotFound]":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLAnesthesiaConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLAnesthesiaEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLAnesthesiaEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLAnesthesiaEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLAnesthesia, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLAnesthesia":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLMeasurementPeriod(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        measurement_period_start: "Union[date, Empty]" = Empty(),
        measurement_period_end: "Union[date, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        annotation_task_type: "Union[GQLAnnotationTaskType, Empty]" = Empty(),
        room_ids: "Union[list[str], Empty]" = Empty(),
        days_of_week: "Union[list[GQLDayOfWeek], Empty]" = Empty(),
        iso_days_of_week: "Union[list[int], Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._measurement_period_start = measurement_period_start
        self._measurement_period_end = measurement_period_end
        self._site = site
        self._annotation_task_type = annotation_task_type
        self._room_ids = room_ids
        self._days_of_week = days_of_week
        self._iso_days_of_week = iso_days_of_week

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def measurement_period_start(self) -> "date":
        if isinstance(self._measurement_period_start, Empty):
            raise ValueError("Field measurement_period_start was not selected as part of the query")
        return self._measurement_period_start

    @property
    def measurement_period_end(self) -> "date":
        if isinstance(self._measurement_period_end, Empty):
            raise ValueError("Field measurement_period_end was not selected as part of the query")
        return self._measurement_period_end

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def annotation_task_type(self) -> "GQLAnnotationTaskType":
        if isinstance(self._annotation_task_type, Empty):
            raise ValueError("Field annotation_task_type was not selected as part of the query")
        return self._annotation_task_type

    @property
    def room_ids(self) -> "list[str]":
        if isinstance(self._room_ids, Empty):
            raise ValueError("Field room_ids was not selected as part of the query")
        return self._room_ids

    @property
    def days_of_week(self) -> "list[GQLDayOfWeek]":
        if isinstance(self._days_of_week, Empty):
            raise ValueError("Field days_of_week was not selected as part of the query")
        return self._days_of_week

    @property
    def iso_days_of_week(self) -> "list[int]":
        if isinstance(self._iso_days_of_week, Empty):
            raise ValueError("Field iso_days_of_week was not selected as part of the query")
        return self._iso_days_of_week


class GQLMeasurementPeriodConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLMeasurementPeriodEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLMeasurementPeriodEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLMeasurementPeriodEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLMeasurementPeriod, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLMeasurementPeriod":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLObservationTypeName(GQLClientObject):
    def __init__(
        self,
        type_id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        color: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._type_id = type_id
        self._name = name
        self._color = color

    @property
    def type_id(self) -> "str":
        if isinstance(self._type_id, Empty):
            raise ValueError("Field type_id was not selected as part of the query")
        return self._type_id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def color(self) -> "Union[str, NotFound]":
        if isinstance(self._color, Empty):
            raise ValueError("Field color was not selected as part of the query")
        return self._color


class GQLContactInformationConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLContactInformationEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLContactInformationEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLContactInformationEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLContactInformation, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLContactInformation":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLBlockConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLBlockEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLBlockEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLBlockEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLBlock, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLBlock":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLBlockTimeAvailableIntervalConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLBlockTimeAvailableIntervalEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLBlockTimeAvailableIntervalEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLBlockTimeAvailableIntervalEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLBlockTimeAvailableInterval, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLBlockTimeAvailableInterval":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLBoardConfigConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLBoardConfigEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLBoardConfigEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLBoardConfigEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLBoardConfig, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLBoardConfig":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLBoardConfig(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        page_size: "Union[int, Empty]" = Empty(),
        page_duration: "Union[int, Empty]" = Empty(),
        blur_video: "Union[bool, Empty]" = Empty(),
        updated_time: "Union[datetime, Empty]" = Empty(),
        updated_by_user: "Union[GQLUser, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
        rooms: "Union[Union[list[GQLRoom], NotFound], Empty]" = Empty(),
        enable_video: "Union[bool, Empty]" = Empty(),
        board_view_type: "Union[GQLBoardViewType, Empty]" = Empty(),
        zoom_percent: "Union[int, Empty]" = Empty(),
        show_closed_rooms: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._page_size = page_size
        self._page_duration = page_duration
        self._blur_video = blur_video
        self._updated_time = updated_time
        self._updated_by_user = updated_by_user
        self._site = site
        self._organization = organization
        self._rooms = rooms
        self._enable_video = enable_video
        self._board_view_type = board_view_type
        self._zoom_percent = zoom_percent
        self._show_closed_rooms = show_closed_rooms

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def page_size(self) -> "int":
        if isinstance(self._page_size, Empty):
            raise ValueError("Field page_size was not selected as part of the query")
        return self._page_size

    @property
    def page_duration(self) -> "int":
        if isinstance(self._page_duration, Empty):
            raise ValueError("Field page_duration was not selected as part of the query")
        return self._page_duration

    @property
    def blur_video(self) -> "bool":
        if isinstance(self._blur_video, Empty):
            raise ValueError("Field blur_video was not selected as part of the query")
        return self._blur_video

    @property
    def updated_time(self) -> "datetime":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def updated_by_user(self) -> "GQLUser":
        if isinstance(self._updated_by_user, Empty):
            raise ValueError("Field updated_by_user was not selected as part of the query")
        return self._updated_by_user

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization

    @property
    def rooms(self) -> "Union[Union[list[GQLRoom], NotFound]]":
        if isinstance(self._rooms, Empty):
            raise ValueError("Field rooms was not selected as part of the query")
        return self._rooms

    @property
    def enable_video(self) -> "bool":
        if isinstance(self._enable_video, Empty):
            raise ValueError("Field enable_video was not selected as part of the query")
        return self._enable_video

    @property
    def board_view_type(self) -> "GQLBoardViewType":
        if isinstance(self._board_view_type, Empty):
            raise ValueError("Field board_view_type was not selected as part of the query")
        return self._board_view_type

    @property
    def zoom_percent(self) -> "int":
        if isinstance(self._zoom_percent, Empty):
            raise ValueError("Field zoom_percent was not selected as part of the query")
        return self._zoom_percent

    @property
    def show_closed_rooms(self) -> "Union[bool, NotFound]":
        if isinstance(self._show_closed_rooms, Empty):
            raise ValueError("Field show_closed_rooms was not selected as part of the query")
        return self._show_closed_rooms


class GQLCaseDurationSurgeonProcedureMappingConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseDurationSurgeonProcedureMappingEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseDurationSurgeonProcedureMappingEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseDurationSurgeonProcedureMappingEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseDurationSurgeonProcedureMapping, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseDurationSurgeonProcedureMapping":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseDurationSurgeonProcedureMapping(GQLClientObject):
    def __init__(
        self,
        surgeon_id: "Union[str, Empty]" = Empty(),
        surgeon: "Union[str, Empty]" = Empty(),
        procedure: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._surgeon_id = surgeon_id
        self._surgeon = surgeon
        self._procedure = procedure

    @property
    def surgeon_id(self) -> "str":
        if isinstance(self._surgeon_id, Empty):
            raise ValueError("Field surgeon_id was not selected as part of the query")
        return self._surgeon_id

    @property
    def surgeon(self) -> "str":
        if isinstance(self._surgeon, Empty):
            raise ValueError("Field surgeon was not selected as part of the query")
        return self._surgeon

    @property
    def procedure(self) -> "str":
        if isinstance(self._procedure, Empty):
            raise ValueError("Field procedure was not selected as part of the query")
        return self._procedure


class GQLCaseDurationTurnoverPrediction(GQLClientObject):
    def __init__(
        self,
        meta: "Union[GQLPredictionMetadata, NotFound, Empty]" = Empty(),
        before_case: "Union[int, Empty]" = Empty(),
        after_case: "Union[int, Empty]" = Empty(),
        open_before_case: "Union[int, Empty]" = Empty(),
        clean_after_case: "Union[int, Empty]" = Empty(),
    ) -> None:
        self._meta = meta
        self._before_case = before_case
        self._after_case = after_case
        self._open_before_case = open_before_case
        self._clean_after_case = clean_after_case

    @property
    def meta(self) -> "Union[GQLPredictionMetadata, NotFound]":
        if isinstance(self._meta, Empty):
            raise ValueError("Field meta was not selected as part of the query")
        return self._meta

    @property
    def before_case(self) -> "int":
        if isinstance(self._before_case, Empty):
            raise ValueError("Field before_case was not selected as part of the query")
        return self._before_case

    @property
    def after_case(self) -> "int":
        if isinstance(self._after_case, Empty):
            raise ValueError("Field after_case was not selected as part of the query")
        return self._after_case

    @property
    def open_before_case(self) -> "int":
        if isinstance(self._open_before_case, Empty):
            raise ValueError("Field open_before_case was not selected as part of the query")
        return self._open_before_case

    @property
    def clean_after_case(self) -> "int":
        if isinstance(self._clean_after_case, Empty):
            raise ValueError("Field clean_after_case was not selected as part of the query")
        return self._clean_after_case


class GQLPredictionMetadata(GQLClientObject):
    def __init__(
        self,
        surgeon_name: "Union[str, Empty]" = Empty(),
        procedure_name: "Union[str, Empty]" = Empty(),
        additional_procedures: "Union[list[Union[str, Empty, None]], Empty]" = Empty(),
    ) -> None:
        self._surgeon_name = surgeon_name
        self._procedure_name = procedure_name
        self._additional_procedures = additional_procedures

    @property
    def surgeon_name(self) -> "str":
        if isinstance(self._surgeon_name, Empty):
            raise ValueError("Field surgeon_name was not selected as part of the query")
        return self._surgeon_name

    @property
    def procedure_name(self) -> "str":
        if isinstance(self._procedure_name, Empty):
            raise ValueError("Field procedure_name was not selected as part of the query")
        return self._procedure_name

    @property
    def additional_procedures(self) -> "Union[list[Union[str, Empty, None]]]":
        if isinstance(self._additional_procedures, Empty):
            raise ValueError("Field additional_procedures was not selected as part of the query")
        return self._additional_procedures


class GQLCaseDurationPredictions(GQLClientObject):
    def __init__(
        self,
        meta: "Union[GQLPredictionMetadata, NotFound, Empty]" = Empty(),
        standard: "Union[int, Empty]" = Empty(),
        complex: "Union[int, Empty]" = Empty(),
        samples: "Union[list[Union[float, Empty]], Empty]" = Empty(),
    ) -> None:
        self._meta = meta
        self._standard = standard
        self._complex = complex
        self._samples = samples

    @property
    def meta(self) -> "Union[GQLPredictionMetadata, NotFound]":
        if isinstance(self._meta, Empty):
            raise ValueError("Field meta was not selected as part of the query")
        return self._meta

    @property
    def standard(self) -> "int":
        if isinstance(self._standard, Empty):
            raise ValueError("Field standard was not selected as part of the query")
        return self._standard

    @property
    def complex(self) -> "int":
        if isinstance(self._complex, Empty):
            raise ValueError("Field complex was not selected as part of the query")
        return self._complex

    @property
    def samples(self) -> "Union[list[Union[float, Empty]]]":
        if isinstance(self._samples, Empty):
            raise ValueError("Field samples was not selected as part of the query")
        return self._samples


class GQLCaseDurationSurgeonOptionConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseDurationSurgeonOptionEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseDurationSurgeonOptionEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseDurationSurgeonOptionEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseDurationSurgeonOption, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseDurationSurgeonOption":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseDurationSurgeonOption(GQLClientObject):
    def __init__(
        self,
        surgeon_id: "Union[str, Empty]" = Empty(),
        surgeon_name: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._surgeon_id = surgeon_id
        self._surgeon_name = surgeon_name

    @property
    def surgeon_id(self) -> "str":
        if isinstance(self._surgeon_id, Empty):
            raise ValueError("Field surgeon_id was not selected as part of the query")
        return self._surgeon_id

    @property
    def surgeon_name(self) -> "str":
        if isinstance(self._surgeon_name, Empty):
            raise ValueError("Field surgeon_name was not selected as part of the query")
        return self._surgeon_name


class GQLCaseDurationProcedureOptionConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseDurationProcedureOptionEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseDurationProcedureOptionEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseDurationProcedureOptionEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseDurationProcedureOption, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseDurationProcedureOption":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseDurationProcedureOption(GQLClientObject):
    def __init__(
        self,
        procedure_name: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._procedure_name = procedure_name

    @property
    def procedure_name(self) -> "str":
        if isinstance(self._procedure_name, Empty):
            raise ValueError("Field procedure_name was not selected as part of the query")
        return self._procedure_name


class GQLAvailableTimeSlot(GQLClientObject):
    def __init__(
        self,
        room_id: "Union[str, Empty]" = Empty(),
        start_time: "Union[datetime, Empty]" = Empty(),
        end_time: "Union[datetime, Empty]" = Empty(),
        block_time_ids: "Union[list[str], Empty]" = Empty(),
        max_available_duration: "Union[timedelta, Empty]" = Empty(),
    ) -> None:
        self._room_id = room_id
        self._start_time = start_time
        self._end_time = end_time
        self._block_time_ids = block_time_ids
        self._max_available_duration = max_available_duration

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def start_time(self) -> "datetime":
        if isinstance(self._start_time, Empty):
            raise ValueError("Field start_time was not selected as part of the query")
        return self._start_time

    @property
    def end_time(self) -> "datetime":
        if isinstance(self._end_time, Empty):
            raise ValueError("Field end_time was not selected as part of the query")
        return self._end_time

    @property
    def block_time_ids(self) -> "list[str]":
        if isinstance(self._block_time_ids, Empty):
            raise ValueError("Field block_time_ids was not selected as part of the query")
        return self._block_time_ids

    @property
    def max_available_duration(self) -> "timedelta":
        if isinstance(self._max_available_duration, Empty):
            raise ValueError("Field max_available_duration was not selected as part of the query")
        return self._max_available_duration


class GQLUserFilterView(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        url: "Union[str, Empty]" = Empty(),
        created_time: "Union[datetime, Empty]" = Empty(),
        updated_time: "Union[datetime, Empty]" = Empty(),
        user_id: "Union[str, Empty]" = Empty(),
        org_id: "Union[str, Empty]" = Empty(),
        organization: "Union[GQLOrganization, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._name = name
        self._url = url
        self._created_time = created_time
        self._updated_time = updated_time
        self._user_id = user_id
        self._org_id = org_id
        self._organization = organization

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def url(self) -> "str":
        if isinstance(self._url, Empty):
            raise ValueError("Field url was not selected as part of the query")
        return self._url

    @property
    def created_time(self) -> "datetime":
        if isinstance(self._created_time, Empty):
            raise ValueError("Field created_time was not selected as part of the query")
        return self._created_time

    @property
    def updated_time(self) -> "datetime":
        if isinstance(self._updated_time, Empty):
            raise ValueError("Field updated_time was not selected as part of the query")
        return self._updated_time

    @property
    def user_id(self) -> "str":
        if isinstance(self._user_id, Empty):
            raise ValueError("Field user_id was not selected as part of the query")
        return self._user_id

    @property
    def org_id(self) -> "str":
        if isinstance(self._org_id, Empty):
            raise ValueError("Field org_id was not selected as part of the query")
        return self._org_id

    @property
    def organization(self) -> "GQLOrganization":
        if isinstance(self._organization, Empty):
            raise ValueError("Field organization was not selected as part of the query")
        return self._organization


class GQLCaseForecastConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseForecastEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseForecastEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseForecastEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseForecast, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseForecast":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLEventDashboardVisibility(GQLClientObject):
    def __init__(
        self,
        event_type_id: "Union[str, Empty]" = Empty(),
        org_id_filter: "Union[Union[list[str], NotFound], Empty]" = Empty(),
    ) -> None:
        self._event_type_id = event_type_id
        self._org_id_filter = org_id_filter

    @property
    def event_type_id(self) -> "str":
        if isinstance(self._event_type_id, Empty):
            raise ValueError("Field event_type_id was not selected as part of the query")
        return self._event_type_id

    @property
    def org_id_filter(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._org_id_filter, Empty):
            raise ValueError("Field org_id_filter was not selected as part of the query")
        return self._org_id_filter


class GQLCaseToBlockConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseToBlockEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseToBlockEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseToBlockEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseToBlock, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseToBlock":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLCaseToBlock(GQLClientObject):
    def __init__(
        self,
        case_id: "Union[str, Empty]" = Empty(),
        scheduled_case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
        block_id: "Union[str, Empty]" = Empty(),
        override: "Union[GQLCaseToBlockOverride, NotFound, Empty]" = Empty(),
        block_date: "Union[date, Empty]" = Empty(),
        score: "Union[int, Empty]" = Empty(),
        case_seconds: "Union[int, NotFound, Empty]" = Empty(),
        utilized_case_seconds: "Union[int, NotFound, Empty]" = Empty(),
        actual_case_seconds: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._case_id = case_id
        self._scheduled_case = scheduled_case
        self._block_id = block_id
        self._override = override
        self._block_date = block_date
        self._score = score
        self._case_seconds = case_seconds
        self._utilized_case_seconds = utilized_case_seconds
        self._actual_case_seconds = actual_case_seconds

    @property
    def case_id(self) -> "str":
        if isinstance(self._case_id, Empty):
            raise ValueError("Field case_id was not selected as part of the query")
        return self._case_id

    @property
    def scheduled_case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._scheduled_case, Empty):
            raise ValueError("Field scheduled_case was not selected as part of the query")
        return self._scheduled_case

    @property
    def block_id(self) -> "str":
        if isinstance(self._block_id, Empty):
            raise ValueError("Field block_id was not selected as part of the query")
        return self._block_id

    @property
    def override(self) -> "Union[GQLCaseToBlockOverride, NotFound]":
        if isinstance(self._override, Empty):
            raise ValueError("Field override was not selected as part of the query")
        return self._override

    @property
    def block_date(self) -> "date":
        if isinstance(self._block_date, Empty):
            raise ValueError("Field block_date was not selected as part of the query")
        return self._block_date

    @property
    def score(self) -> "int":
        if isinstance(self._score, Empty):
            raise ValueError("Field score was not selected as part of the query")
        return self._score

    @property
    def case_seconds(self) -> "Union[int, NotFound]":
        if isinstance(self._case_seconds, Empty):
            raise ValueError("Field case_seconds was not selected as part of the query")
        return self._case_seconds

    @property
    def utilized_case_seconds(self) -> "Union[int, NotFound]":
        if isinstance(self._utilized_case_seconds, Empty):
            raise ValueError("Field utilized_case_seconds was not selected as part of the query")
        return self._utilized_case_seconds

    @property
    def actual_case_seconds(self) -> "Union[int, NotFound]":
        if isinstance(self._actual_case_seconds, Empty):
            raise ValueError("Field actual_case_seconds was not selected as part of the query")
        return self._actual_case_seconds


class GQLCaseToBlockOverride(GQLClientObject):
    def __init__(
        self,
        case_id: "Union[str, Empty]" = Empty(),
        block_id: "Union[str, Empty]" = Empty(),
        block_date: "Union[date, Empty]" = Empty(),
        utilized_procedure_minutes: "Union[int, NotFound, Empty]" = Empty(),
        utilized_turnover_minutes: "Union[int, NotFound, Empty]" = Empty(),
        user_id: "Union[str, Empty]" = Empty(),
        note: "Union[str, NotFound, Empty]" = Empty(),
        user: "Union[GQLUser, Empty]" = Empty(),
    ) -> None:
        self._case_id = case_id
        self._block_id = block_id
        self._block_date = block_date
        self._utilized_procedure_minutes = utilized_procedure_minutes
        self._utilized_turnover_minutes = utilized_turnover_minutes
        self._user_id = user_id
        self._note = note
        self._user = user

    @property
    def case_id(self) -> "str":
        if isinstance(self._case_id, Empty):
            raise ValueError("Field case_id was not selected as part of the query")
        return self._case_id

    @property
    def block_id(self) -> "str":
        if isinstance(self._block_id, Empty):
            raise ValueError("Field block_id was not selected as part of the query")
        return self._block_id

    @property
    def block_date(self) -> "date":
        if isinstance(self._block_date, Empty):
            raise ValueError("Field block_date was not selected as part of the query")
        return self._block_date

    @property
    def utilized_procedure_minutes(self) -> "Union[int, NotFound]":
        if isinstance(self._utilized_procedure_minutes, Empty):
            raise ValueError(
                "Field utilized_procedure_minutes was not selected as part of the query"
            )
        return self._utilized_procedure_minutes

    @property
    def utilized_turnover_minutes(self) -> "Union[int, NotFound]":
        if isinstance(self._utilized_turnover_minutes, Empty):
            raise ValueError(
                "Field utilized_turnover_minutes was not selected as part of the query"
            )
        return self._utilized_turnover_minutes

    @property
    def user_id(self) -> "str":
        if isinstance(self._user_id, Empty):
            raise ValueError("Field user_id was not selected as part of the query")
        return self._user_id

    @property
    def note(self) -> "Union[str, NotFound]":
        if isinstance(self._note, Empty):
            raise ValueError("Field note was not selected as part of the query")
        return self._note

    @property
    def user(self) -> "GQLUser":
        if isinstance(self._user, Empty):
            raise ValueError("Field user was not selected as part of the query")
        return self._user


class GQLBlockUtilization(GQLClientObject):
    def __init__(
        self,
        date: "Union[date, Empty]" = Empty(),
        block_id: "Union[str, Empty]" = Empty(),
        cases_for_block_day: "Union[list[Union[GQLCaseToBlock, Empty]], Empty]" = Empty(),
        utilized_scheduled_seconds: "Union[int, Empty]" = Empty(),
        total_scheduled_case_seconds: "Union[int, Empty]" = Empty(),
        utilized_seconds: "Union[int, Empty]" = Empty(),
        total_case_seconds: "Union[int, NotFound, Empty]" = Empty(),
        total_actual_case_seconds: "Union[int, Empty]" = Empty(),
        available_seconds: "Union[int, Empty]" = Empty(),
        total_block_seconds: "Union[int, Empty]" = Empty(),
        block: "Union[GQLBlock, Empty]" = Empty(),
    ) -> None:
        self._date = date
        self._block_id = block_id
        self._cases_for_block_day = cases_for_block_day
        self._utilized_scheduled_seconds = utilized_scheduled_seconds
        self._total_scheduled_case_seconds = total_scheduled_case_seconds
        self._utilized_seconds = utilized_seconds
        self._total_case_seconds = total_case_seconds
        self._total_actual_case_seconds = total_actual_case_seconds
        self._available_seconds = available_seconds
        self._total_block_seconds = total_block_seconds
        self._block = block

    @property
    def date(self) -> "date":
        if isinstance(self._date, Empty):
            raise ValueError("Field date was not selected as part of the query")
        return self._date

    @property
    def block_id(self) -> "str":
        if isinstance(self._block_id, Empty):
            raise ValueError("Field block_id was not selected as part of the query")
        return self._block_id

    @property
    def cases_for_block_day(self) -> "Union[list[Union[GQLCaseToBlock, Empty]]]":
        if isinstance(self._cases_for_block_day, Empty):
            raise ValueError("Field cases_for_block_day was not selected as part of the query")
        return self._cases_for_block_day

    @property
    def utilized_scheduled_seconds(self) -> "int":
        if isinstance(self._utilized_scheduled_seconds, Empty):
            raise ValueError(
                "Field utilized_scheduled_seconds was not selected as part of the query"
            )
        return self._utilized_scheduled_seconds

    @property
    def total_scheduled_case_seconds(self) -> "int":
        if isinstance(self._total_scheduled_case_seconds, Empty):
            raise ValueError(
                "Field total_scheduled_case_seconds was not selected as part of the query"
            )
        return self._total_scheduled_case_seconds

    @property
    def utilized_seconds(self) -> "int":
        if isinstance(self._utilized_seconds, Empty):
            raise ValueError("Field utilized_seconds was not selected as part of the query")
        return self._utilized_seconds

    @property
    def total_case_seconds(self) -> "Union[int, NotFound]":
        if isinstance(self._total_case_seconds, Empty):
            raise ValueError("Field total_case_seconds was not selected as part of the query")
        return self._total_case_seconds

    @property
    def total_actual_case_seconds(self) -> "int":
        if isinstance(self._total_actual_case_seconds, Empty):
            raise ValueError(
                "Field total_actual_case_seconds was not selected as part of the query"
            )
        return self._total_actual_case_seconds

    @property
    def available_seconds(self) -> "int":
        if isinstance(self._available_seconds, Empty):
            raise ValueError("Field available_seconds was not selected as part of the query")
        return self._available_seconds

    @property
    def total_block_seconds(self) -> "int":
        if isinstance(self._total_block_seconds, Empty):
            raise ValueError("Field total_block_seconds was not selected as part of the query")
        return self._total_block_seconds

    @property
    def block(self) -> "GQLBlock":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block


class GQLCustomPhaseConfig(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        start_event_type: "Union[str, Empty]" = Empty(),
        end_event_type: "Union[str, Empty]" = Empty(),
        name: "Union[str, Empty]" = Empty(),
        description: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._start_event_type = start_event_type
        self._end_event_type = end_event_type
        self._name = name
        self._description = description

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def start_event_type(self) -> "str":
        if isinstance(self._start_event_type, Empty):
            raise ValueError("Field start_event_type was not selected as part of the query")
        return self._start_event_type

    @property
    def end_event_type(self) -> "str":
        if isinstance(self._end_event_type, Empty):
            raise ValueError("Field end_event_type was not selected as part of the query")
        return self._end_event_type

    @property
    def name(self) -> "str":
        if isinstance(self._name, Empty):
            raise ValueError("Field name was not selected as part of the query")
        return self._name

    @property
    def description(self) -> "str":
        if isinstance(self._description, Empty):
            raise ValueError("Field description was not selected as part of the query")
        return self._description


class GQLTerminalCleanScore(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, Empty]" = Empty(),
        room_id: "Union[str, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        date: "Union[date, Empty]" = Empty(),
        score: "Union[GQLCleanScoreEnum, NotFound, Empty]" = Empty(),
        comments: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._id = id
        self._room_id = room_id
        self._room = room
        self._date = date
        self._score = score
        self._comments = comments

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def room_id(self) -> "str":
        if isinstance(self._room_id, Empty):
            raise ValueError("Field room_id was not selected as part of the query")
        return self._room_id

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def date(self) -> "date":
        if isinstance(self._date, Empty):
            raise ValueError("Field date was not selected as part of the query")
        return self._date

    @property
    def score(self) -> "Union[GQLCleanScoreEnum, NotFound]":
        if isinstance(self._score, Empty):
            raise ValueError("Field score was not selected as part of the query")
        return self._score

    @property
    def comments(self) -> "str":
        if isinstance(self._comments, Empty):
            raise ValueError("Field comments was not selected as part of the query")
        return self._comments


class GQLMutation(GQLClientObject):
    def __init__(
        self,
        event_create: "Union[GQLEventCreate, NotFound, Empty]" = Empty(),
        event_update: "Union[GQLEventUpdate, NotFound, Empty]" = Empty(),
        event_delete: "Union[GQLEventDelete, NotFound, Empty]" = Empty(),
        event_upsert: "Union[GQLEventUpsert, NotFound, Empty]" = Empty(),
        event_type_create: "Union[GQLEventTypeCreate, NotFound, Empty]" = Empty(),
        event_type_update: "Union[GQLEventTypeUpdate, NotFound, Empty]" = Empty(),
        event_label_option_create: "Union[GQLEventLabelOptionCreate, NotFound, Empty]" = Empty(),
        event_label_option_update: "Union[GQLEventLabelOptionUpdate, NotFound, Empty]" = Empty(),
        event_label_option_delete: "Union[GQLEventLabelOptionDelete, NotFound, Empty]" = Empty(),
        highlight_create: "Union[GQLHighlightCreate, NotFound, Empty]" = Empty(),
        highlight_update: "Union[GQLHighlightUpdate, NotFound, Empty]" = Empty(),
        highlight_delete: "Union[GQLHighlightDelete, NotFound, Empty]" = Empty(),
        highlight_archive: "Union[GQLHighlightArchive, NotFound, Empty]" = Empty(),
        highlight_feedback_create: "Union[GQLHighlightFeedbackCreate, NotFound, Empty]" = Empty(),
        highlight_feedback_update: "Union[GQLHighlightFeedbackUpdate, NotFound, Empty]" = Empty(),
        annotation_task_bulk_generate: "Union[GQLAnnotationTaskBulkGenerate, NotFound, Empty]" = Empty(),
        annotation_task_bulk_update: "Union[GQLAnnotationTaskBulkUpdate, NotFound, Empty]" = Empty(),
        annotation_task_update: "Union[GQLAnnotationTaskUpdate, NotFound, Empty]" = Empty(),
        annotation_task_exit: "Union[GQLAnnotationTaskExit, NotFound, Empty]" = Empty(),
        annotation_task_type_create: "Union[GQLAnnnotationTaskTypeCreate, NotFound, Empty]" = Empty(),
        annotation_task_type_update: "Union[GQLAnnnotationTaskTypeUpdate, NotFound, Empty]" = Empty(),
        annotation_task_schedule_create: "Union[GQLAnnotationTaskScheduleCreate, NotFound, Empty]" = Empty(),
        annotation_task_schedule_update: "Union[GQLAnnotationTaskScheduleUpdate, NotFound, Empty]" = Empty(),
        annotation_task_next_annotate: "Union[GQLAnnotationTaskNextAnnotate, NotFound, Empty]" = Empty(),
        annotation_task_next_review: "Union[GQLAnnotationTaskNextReview, NotFound, Empty]" = Empty(),
        camera_capture_latest_image: "Union[GQLCameraCaptureLatestImage, NotFound, Empty]" = Empty(),
        case_procedures_upsert: "Union[GQLCaseProceduresUpsert, NotFound, Empty]" = Empty(),
        process_case_derived_properties: "Union[GQLProcessCaseDerivedProperties, NotFound, Empty]" = Empty(),
        case_note_plan_upsert: "Union[GQLCaseNotePlanUpsert, NotFound, Empty]" = Empty(),
        case_procedures_upsert_and_archive: "Union[GQLCaseProceduresUpsertAndArchive, NotFound, Empty]" = Empty(),
        case_staff_upsert: "Union[GQLCaseStaffUpsert, NotFound, Empty]" = Empty(),
        case_staff_upsert_and_archive: "Union[GQLCaseStaffUpsertAndArchive, NotFound, Empty]" = Empty(),
        case_staff_plan_upsert: "Union[GQLCaseStaffPlanUpsert, NotFound, Empty]" = Empty(),
        case_flag_upsert: "Union[GQLCaseFlagUpsert, Empty]" = Empty(),
        match_cases: "Union[GQLMatchCases, NotFound, Empty]" = Empty(),
        procedures_upsert: "Union[GQLProceduresUpsert, NotFound, Empty]" = Empty(),
        anesthesias_upsert: "Union[GQLAnesthesiasUpsert, NotFound, Empty]" = Empty(),
        measurement_period_delete: "Union[GQLMeasurementPeriodDelete, NotFound, Empty]" = Empty(),
        measurement_period_upsert: "Union[GQLMeasurementPeriodUpsert, NotFound, Empty]" = Empty(),
        staff_upsert: "Union[GQLStaffUpsert, NotFound, Empty]" = Empty(),
        staff_codes_upsert: "Union[GQLStaffCodesUpsert, NotFound, Empty]" = Empty(),
        phase_delete: "Union[GQLPhaseDelete, NotFound, Empty]" = Empty(),
        phase_update: "Union[GQLPhaseUpdate, NotFound, Empty]" = Empty(),
        phase_create: "Union[GQLPhaseCreate, NotFound, Empty]" = Empty(),
        phase_upsert: "Union[GQLPhaseUpsert, NotFound, Empty]" = Empty(),
        phase_relationship_delete: "Union[GQLPhaseRelationshipDelete, NotFound, Empty]" = Empty(),
        phase_relationship_create: "Union[GQLPhaseRelationshipCreate, NotFound, Empty]" = Empty(),
        observation_create: "Union[GQLObservationCreate, NotFound, Empty]" = Empty(),
        observation_delete: "Union[GQLObservationDelete, NotFound, Empty]" = Empty(),
        observations_upsert: "Union[GQLObservationsUpsert, NotFound, Empty]" = Empty(),
        subscribers_upsert: "Union[GQLSubscribersUpsert, NotFound, Empty]" = Empty(),
        staffing_needs_ratio_create: "Union[GQLStaffingNeedsRatioCreate, NotFound, Empty]" = Empty(),
        notify_staff_for_events: "Union[GQLNotifyStaffForEvents, NotFound, Empty]" = Empty(),
        check_notifications_errors: "Union[GQLCheckNotificationsErrors, NotFound, Empty]" = Empty(),
        email_available_times: "Union[GQLEmailAvailableTimes, NotFound, Empty]" = Empty(),
        email_available_times_html: "Union[GQLEmailAvailableTimesHtml, NotFound, Empty]" = Empty(),
        block_create: "Union[GQLBlockCreate, NotFound, Empty]" = Empty(),
        block_update: "Union[GQLBlockUpdate, NotFound, Empty]" = Empty(),
        block_archive: "Union[GQLBlockArchive, NotFound, Empty]" = Empty(),
        block_unarchive: "Union[GQLBlockUnarchive, NotFound, Empty]" = Empty(),
        block_time_bulk_create: "Union[GQLBlockTimeBulkCreate, NotFound, Empty]" = Empty(),
        block_releases_reprocess: "Union[GQLBlockReleaseReprocess, NotFound, Empty]" = Empty(),
        block_release_process_date_range: "Union[GQLBlockReleaseProcessDateRange, NotFound, Empty]" = Empty(),
        block_release_file_transform: "Union[GQLBlockReleaseFileTransform, NotFound, Empty]" = Empty(),
        block_time_bulk_delete_duplicate: "Union[GQLBlockTimeBulkDeleteDuplicate, NotFound, Empty]" = Empty(),
        board_config_create: "Union[GQLBoardConfigCreate, NotFound, Empty]" = Empty(),
        board_config_update: "Union[GQLBoardConfigUpdate, NotFound, Empty]" = Empty(),
        board_config_delete: "Union[GQLBoardConfigDelete, NotFound, Empty]" = Empty(),
        organization_create: "Union[GQLOrganizationCreate, NotFound, Empty]" = Empty(),
        organization_update: "Union[GQLOrganizationUpdate, NotFound, Empty]" = Empty(),
        site_create: "Union[GQLSiteCreate, NotFound, Empty]" = Empty(),
        site_update: "Union[GQLSiteUpdate, NotFound, Empty]" = Empty(),
        site_prime_time_config_upsert: "Union[GQLSitePrimeTimeConfigUpsert, NotFound, Empty]" = Empty(),
        site_closure_create: "Union[GQLSiteClosureCreate, NotFound, Empty]" = Empty(),
        site_closure_delete: "Union[GQLSiteClosureDelete, NotFound, Empty]" = Empty(),
        default_site_closures_create: "Union[GQLDefaultSiteClosuresCreate, NotFound, Empty]" = Empty(),
        site_first_case_config_upsert: "Union[GQLSiteFirstCaseConfigUpsert, NotFound, Empty]" = Empty(),
        site_launches_upsert: "Union[GQLSiteLaunchesUpsert, NotFound, Empty]" = Empty(),
        room_create: "Union[GQLRoomCreate, NotFound, Empty]" = Empty(),
        rooms_create: "Union[GQLRoomsCreate, NotFound, Empty]" = Empty(),
        room_update: "Union[GQLRoomUpdate, NotFound, Empty]" = Empty(),
        room_update_configuration: "Union[GQLRoomUpdateConfiguration, NotFound, Empty]" = Empty(),
        room_tag_create: "Union[GQLRoomTagCreate, NotFound, Empty]" = Empty(),
        room_set_tags: "Union[GQLRoomSetTags, NotFound, Empty]" = Empty(),
        room_tag_rename: "Union[GQLRoomTagRename, NotFound, Empty]" = Empty(),
        room_tag_update: "Union[GQLRoomTagUpdate, NotFound, Empty]" = Empty(),
        room_closure_create: "Union[GQLRoomClosureCreate, NotFound, Empty]" = Empty(),
        room_closure_delete: "Union[GQLRoomClosureDelete, NotFound, Empty]" = Empty(),
        room_prime_time_config_upsert: "Union[GQLRoomPrimeTimeConfigUpsert, NotFound, Empty]" = Empty(),
        room_prime_time_config_delete: "Union[GQLRoomPrimeTimeConfigDelete, NotFound, Empty]" = Empty(),
        room_first_case_config_upsert: "Union[GQLRoomFirstCaseConfigUpsert, NotFound, Empty]" = Empty(),
        room_first_case_config_delete: "Union[GQLRoomFirstCaseConfigDelete, NotFound, Empty]" = Empty(),
        camera_create: "Union[GQLCameraCreate, NotFound, Empty]" = Empty(),
        cameras_create: "Union[GQLCamerasCreate, NotFound, Empty]" = Empty(),
        camera_update: "Union[GQLCameraUpdate, NotFound, Empty]" = Empty(),
        user_filter_view_create: "Union[GQLUserFilterViewCreate, NotFound, Empty]" = Empty(),
        user_filter_view_update: "Union[GQLUserFilterViewUpdate, NotFound, Empty]" = Empty(),
        user_filter_view_delete: "Union[GQLUserFilterViewDelete, NotFound, Empty]" = Empty(),
        cluster_create: "Union[GQLClusterCreate, NotFound, Empty]" = Empty(),
        cluster_update: "Union[GQLClusterUpdate, NotFound, Empty]" = Empty(),
        turnover_goals_update: "Union[GQLTurnoverGoalsUpdate, NotFound, Empty]" = Empty(),
        case_forecast_upsert: "Union[GQLCaseForecastUpsert, NotFound, Empty]" = Empty(),
        upsert_forecasts_for_cases: "Union[GQLUpsertForecastsForCases, NotFound, Empty]" = Empty(),
        event_dashboard_visibility_upsert: "Union[GQLEventDashboardVisibilityUpsert, NotFound, Empty]" = Empty(),
        event_dashboard_visibility_delete: "Union[GQLEventDashboardVisibilityDelete, NotFound, Empty]" = Empty(),
        terminal_clean_score_upsert: "Union[GQLTerminalCleanScoreUpsert, NotFound, Empty]" = Empty(),
        turnover_label_note_upsert: "Union[GQLTurnoverLabelNoteUpsert, NotFound, Empty]" = Empty(),
        case_to_block_overrides_upsert: "Union[GQLCaseToBlockOverridesUpsert, NotFound, Empty]" = Empty(),
        custom_phase_config_upsert: "Union[GQLCustomPhaseConfigUpsert, NotFound, Empty]" = Empty(),
        custom_phase_config_delete: "Union[GQLCustomPhaseConfigDelete, NotFound, Empty]" = Empty(),
        case_label_upsert: "Union[GQLCaseLabelUpsert, NotFound, Empty]" = Empty(),
    ) -> None:
        self._event_create = event_create
        self._event_update = event_update
        self._event_delete = event_delete
        self._event_upsert = event_upsert
        self._event_type_create = event_type_create
        self._event_type_update = event_type_update
        self._event_label_option_create = event_label_option_create
        self._event_label_option_update = event_label_option_update
        self._event_label_option_delete = event_label_option_delete
        self._highlight_create = highlight_create
        self._highlight_update = highlight_update
        self._highlight_delete = highlight_delete
        self._highlight_archive = highlight_archive
        self._highlight_feedback_create = highlight_feedback_create
        self._highlight_feedback_update = highlight_feedback_update
        self._annotation_task_bulk_generate = annotation_task_bulk_generate
        self._annotation_task_bulk_update = annotation_task_bulk_update
        self._annotation_task_update = annotation_task_update
        self._annotation_task_exit = annotation_task_exit
        self._annotation_task_type_create = annotation_task_type_create
        self._annotation_task_type_update = annotation_task_type_update
        self._annotation_task_schedule_create = annotation_task_schedule_create
        self._annotation_task_schedule_update = annotation_task_schedule_update
        self._annotation_task_next_annotate = annotation_task_next_annotate
        self._annotation_task_next_review = annotation_task_next_review
        self._camera_capture_latest_image = camera_capture_latest_image
        self._case_procedures_upsert = case_procedures_upsert
        self._process_case_derived_properties = process_case_derived_properties
        self._case_note_plan_upsert = case_note_plan_upsert
        self._case_procedures_upsert_and_archive = case_procedures_upsert_and_archive
        self._case_staff_upsert = case_staff_upsert
        self._case_staff_upsert_and_archive = case_staff_upsert_and_archive
        self._case_staff_plan_upsert = case_staff_plan_upsert
        self._case_flag_upsert = case_flag_upsert
        self._match_cases = match_cases
        self._procedures_upsert = procedures_upsert
        self._anesthesias_upsert = anesthesias_upsert
        self._measurement_period_delete = measurement_period_delete
        self._measurement_period_upsert = measurement_period_upsert
        self._staff_upsert = staff_upsert
        self._staff_codes_upsert = staff_codes_upsert
        self._phase_delete = phase_delete
        self._phase_update = phase_update
        self._phase_create = phase_create
        self._phase_upsert = phase_upsert
        self._phase_relationship_delete = phase_relationship_delete
        self._phase_relationship_create = phase_relationship_create
        self._observation_create = observation_create
        self._observation_delete = observation_delete
        self._observations_upsert = observations_upsert
        self._subscribers_upsert = subscribers_upsert
        self._staffing_needs_ratio_create = staffing_needs_ratio_create
        self._notify_staff_for_events = notify_staff_for_events
        self._check_notifications_errors = check_notifications_errors
        self._email_available_times = email_available_times
        self._email_available_times_html = email_available_times_html
        self._block_create = block_create
        self._block_update = block_update
        self._block_archive = block_archive
        self._block_unarchive = block_unarchive
        self._block_time_bulk_create = block_time_bulk_create
        self._block_releases_reprocess = block_releases_reprocess
        self._block_release_process_date_range = block_release_process_date_range
        self._block_release_file_transform = block_release_file_transform
        self._block_time_bulk_delete_duplicate = block_time_bulk_delete_duplicate
        self._board_config_create = board_config_create
        self._board_config_update = board_config_update
        self._board_config_delete = board_config_delete
        self._organization_create = organization_create
        self._organization_update = organization_update
        self._site_create = site_create
        self._site_update = site_update
        self._site_prime_time_config_upsert = site_prime_time_config_upsert
        self._site_closure_create = site_closure_create
        self._site_closure_delete = site_closure_delete
        self._default_site_closures_create = default_site_closures_create
        self._site_first_case_config_upsert = site_first_case_config_upsert
        self._site_launches_upsert = site_launches_upsert
        self._room_create = room_create
        self._rooms_create = rooms_create
        self._room_update = room_update
        self._room_update_configuration = room_update_configuration
        self._room_tag_create = room_tag_create
        self._room_set_tags = room_set_tags
        self._room_tag_rename = room_tag_rename
        self._room_tag_update = room_tag_update
        self._room_closure_create = room_closure_create
        self._room_closure_delete = room_closure_delete
        self._room_prime_time_config_upsert = room_prime_time_config_upsert
        self._room_prime_time_config_delete = room_prime_time_config_delete
        self._room_first_case_config_upsert = room_first_case_config_upsert
        self._room_first_case_config_delete = room_first_case_config_delete
        self._camera_create = camera_create
        self._cameras_create = cameras_create
        self._camera_update = camera_update
        self._user_filter_view_create = user_filter_view_create
        self._user_filter_view_update = user_filter_view_update
        self._user_filter_view_delete = user_filter_view_delete
        self._cluster_create = cluster_create
        self._cluster_update = cluster_update
        self._turnover_goals_update = turnover_goals_update
        self._case_forecast_upsert = case_forecast_upsert
        self._upsert_forecasts_for_cases = upsert_forecasts_for_cases
        self._event_dashboard_visibility_upsert = event_dashboard_visibility_upsert
        self._event_dashboard_visibility_delete = event_dashboard_visibility_delete
        self._terminal_clean_score_upsert = terminal_clean_score_upsert
        self._turnover_label_note_upsert = turnover_label_note_upsert
        self._case_to_block_overrides_upsert = case_to_block_overrides_upsert
        self._custom_phase_config_upsert = custom_phase_config_upsert
        self._custom_phase_config_delete = custom_phase_config_delete
        self._case_label_upsert = case_label_upsert

    @property
    def event_create(self) -> "Union[GQLEventCreate, NotFound]":
        if isinstance(self._event_create, Empty):
            raise ValueError("Field event_create was not selected as part of the query")
        return self._event_create

    @property
    def event_update(self) -> "Union[GQLEventUpdate, NotFound]":
        if isinstance(self._event_update, Empty):
            raise ValueError("Field event_update was not selected as part of the query")
        return self._event_update

    @property
    def event_delete(self) -> "Union[GQLEventDelete, NotFound]":
        if isinstance(self._event_delete, Empty):
            raise ValueError("Field event_delete was not selected as part of the query")
        return self._event_delete

    @property
    def event_upsert(self) -> "Union[GQLEventUpsert, NotFound]":
        if isinstance(self._event_upsert, Empty):
            raise ValueError("Field event_upsert was not selected as part of the query")
        return self._event_upsert

    @property
    def event_type_create(self) -> "Union[GQLEventTypeCreate, NotFound]":
        if isinstance(self._event_type_create, Empty):
            raise ValueError("Field event_type_create was not selected as part of the query")
        return self._event_type_create

    @property
    def event_type_update(self) -> "Union[GQLEventTypeUpdate, NotFound]":
        if isinstance(self._event_type_update, Empty):
            raise ValueError("Field event_type_update was not selected as part of the query")
        return self._event_type_update

    @property
    def event_label_option_create(self) -> "Union[GQLEventLabelOptionCreate, NotFound]":
        if isinstance(self._event_label_option_create, Empty):
            raise ValueError(
                "Field event_label_option_create was not selected as part of the query"
            )
        return self._event_label_option_create

    @property
    def event_label_option_update(self) -> "Union[GQLEventLabelOptionUpdate, NotFound]":
        if isinstance(self._event_label_option_update, Empty):
            raise ValueError(
                "Field event_label_option_update was not selected as part of the query"
            )
        return self._event_label_option_update

    @property
    def event_label_option_delete(self) -> "Union[GQLEventLabelOptionDelete, NotFound]":
        if isinstance(self._event_label_option_delete, Empty):
            raise ValueError(
                "Field event_label_option_delete was not selected as part of the query"
            )
        return self._event_label_option_delete

    @property
    def highlight_create(self) -> "Union[GQLHighlightCreate, NotFound]":
        if isinstance(self._highlight_create, Empty):
            raise ValueError("Field highlight_create was not selected as part of the query")
        return self._highlight_create

    @property
    def highlight_update(self) -> "Union[GQLHighlightUpdate, NotFound]":
        if isinstance(self._highlight_update, Empty):
            raise ValueError("Field highlight_update was not selected as part of the query")
        return self._highlight_update

    @property
    def highlight_delete(self) -> "Union[GQLHighlightDelete, NotFound]":
        if isinstance(self._highlight_delete, Empty):
            raise ValueError("Field highlight_delete was not selected as part of the query")
        return self._highlight_delete

    @property
    def highlight_archive(self) -> "Union[GQLHighlightArchive, NotFound]":
        if isinstance(self._highlight_archive, Empty):
            raise ValueError("Field highlight_archive was not selected as part of the query")
        return self._highlight_archive

    @property
    def highlight_feedback_create(self) -> "Union[GQLHighlightFeedbackCreate, NotFound]":
        if isinstance(self._highlight_feedback_create, Empty):
            raise ValueError(
                "Field highlight_feedback_create was not selected as part of the query"
            )
        return self._highlight_feedback_create

    @property
    def highlight_feedback_update(self) -> "Union[GQLHighlightFeedbackUpdate, NotFound]":
        if isinstance(self._highlight_feedback_update, Empty):
            raise ValueError(
                "Field highlight_feedback_update was not selected as part of the query"
            )
        return self._highlight_feedback_update

    @property
    def annotation_task_bulk_generate(self) -> "Union[GQLAnnotationTaskBulkGenerate, NotFound]":
        if isinstance(self._annotation_task_bulk_generate, Empty):
            raise ValueError(
                "Field annotation_task_bulk_generate was not selected as part of the query"
            )
        return self._annotation_task_bulk_generate

    @property
    def annotation_task_bulk_update(self) -> "Union[GQLAnnotationTaskBulkUpdate, NotFound]":
        if isinstance(self._annotation_task_bulk_update, Empty):
            raise ValueError(
                "Field annotation_task_bulk_update was not selected as part of the query"
            )
        return self._annotation_task_bulk_update

    @property
    def annotation_task_update(self) -> "Union[GQLAnnotationTaskUpdate, NotFound]":
        if isinstance(self._annotation_task_update, Empty):
            raise ValueError("Field annotation_task_update was not selected as part of the query")
        return self._annotation_task_update

    @property
    def annotation_task_exit(self) -> "Union[GQLAnnotationTaskExit, NotFound]":
        if isinstance(self._annotation_task_exit, Empty):
            raise ValueError("Field annotation_task_exit was not selected as part of the query")
        return self._annotation_task_exit

    @property
    def annotation_task_type_create(self) -> "Union[GQLAnnnotationTaskTypeCreate, NotFound]":
        if isinstance(self._annotation_task_type_create, Empty):
            raise ValueError(
                "Field annotation_task_type_create was not selected as part of the query"
            )
        return self._annotation_task_type_create

    @property
    def annotation_task_type_update(self) -> "Union[GQLAnnnotationTaskTypeUpdate, NotFound]":
        if isinstance(self._annotation_task_type_update, Empty):
            raise ValueError(
                "Field annotation_task_type_update was not selected as part of the query"
            )
        return self._annotation_task_type_update

    @property
    def annotation_task_schedule_create(self) -> "Union[GQLAnnotationTaskScheduleCreate, NotFound]":
        if isinstance(self._annotation_task_schedule_create, Empty):
            raise ValueError(
                "Field annotation_task_schedule_create was not selected as part of the query"
            )
        return self._annotation_task_schedule_create

    @property
    def annotation_task_schedule_update(self) -> "Union[GQLAnnotationTaskScheduleUpdate, NotFound]":
        if isinstance(self._annotation_task_schedule_update, Empty):
            raise ValueError(
                "Field annotation_task_schedule_update was not selected as part of the query"
            )
        return self._annotation_task_schedule_update

    @property
    def annotation_task_next_annotate(self) -> "Union[GQLAnnotationTaskNextAnnotate, NotFound]":
        if isinstance(self._annotation_task_next_annotate, Empty):
            raise ValueError(
                "Field annotation_task_next_annotate was not selected as part of the query"
            )
        return self._annotation_task_next_annotate

    @property
    def annotation_task_next_review(self) -> "Union[GQLAnnotationTaskNextReview, NotFound]":
        if isinstance(self._annotation_task_next_review, Empty):
            raise ValueError(
                "Field annotation_task_next_review was not selected as part of the query"
            )
        return self._annotation_task_next_review

    @property
    def camera_capture_latest_image(self) -> "Union[GQLCameraCaptureLatestImage, NotFound]":
        if isinstance(self._camera_capture_latest_image, Empty):
            raise ValueError(
                "Field camera_capture_latest_image was not selected as part of the query"
            )
        return self._camera_capture_latest_image

    @property
    def case_procedures_upsert(self) -> "Union[GQLCaseProceduresUpsert, NotFound]":
        if isinstance(self._case_procedures_upsert, Empty):
            raise ValueError("Field case_procedures_upsert was not selected as part of the query")
        return self._case_procedures_upsert

    @property
    def process_case_derived_properties(self) -> "Union[GQLProcessCaseDerivedProperties, NotFound]":
        if isinstance(self._process_case_derived_properties, Empty):
            raise ValueError(
                "Field process_case_derived_properties was not selected as part of the query"
            )
        return self._process_case_derived_properties

    @property
    def case_note_plan_upsert(self) -> "Union[GQLCaseNotePlanUpsert, NotFound]":
        if isinstance(self._case_note_plan_upsert, Empty):
            raise ValueError("Field case_note_plan_upsert was not selected as part of the query")
        return self._case_note_plan_upsert

    @property
    def case_procedures_upsert_and_archive(
        self,
    ) -> "Union[GQLCaseProceduresUpsertAndArchive, NotFound]":
        if isinstance(self._case_procedures_upsert_and_archive, Empty):
            raise ValueError(
                "Field case_procedures_upsert_and_archive was not selected as part of the query"
            )
        return self._case_procedures_upsert_and_archive

    @property
    def case_staff_upsert(self) -> "Union[GQLCaseStaffUpsert, NotFound]":
        if isinstance(self._case_staff_upsert, Empty):
            raise ValueError("Field case_staff_upsert was not selected as part of the query")
        return self._case_staff_upsert

    @property
    def case_staff_upsert_and_archive(self) -> "Union[GQLCaseStaffUpsertAndArchive, NotFound]":
        if isinstance(self._case_staff_upsert_and_archive, Empty):
            raise ValueError(
                "Field case_staff_upsert_and_archive was not selected as part of the query"
            )
        return self._case_staff_upsert_and_archive

    @property
    def case_staff_plan_upsert(self) -> "Union[GQLCaseStaffPlanUpsert, NotFound]":
        if isinstance(self._case_staff_plan_upsert, Empty):
            raise ValueError("Field case_staff_plan_upsert was not selected as part of the query")
        return self._case_staff_plan_upsert

    @property
    def case_flag_upsert(self) -> "GQLCaseFlagUpsert":
        if isinstance(self._case_flag_upsert, Empty):
            raise ValueError("Field case_flag_upsert was not selected as part of the query")
        return self._case_flag_upsert

    @property
    def match_cases(self) -> "Union[GQLMatchCases, NotFound]":
        if isinstance(self._match_cases, Empty):
            raise ValueError("Field match_cases was not selected as part of the query")
        return self._match_cases

    @property
    def procedures_upsert(self) -> "Union[GQLProceduresUpsert, NotFound]":
        if isinstance(self._procedures_upsert, Empty):
            raise ValueError("Field procedures_upsert was not selected as part of the query")
        return self._procedures_upsert

    @property
    def anesthesias_upsert(self) -> "Union[GQLAnesthesiasUpsert, NotFound]":
        if isinstance(self._anesthesias_upsert, Empty):
            raise ValueError("Field anesthesias_upsert was not selected as part of the query")
        return self._anesthesias_upsert

    @property
    def measurement_period_delete(self) -> "Union[GQLMeasurementPeriodDelete, NotFound]":
        if isinstance(self._measurement_period_delete, Empty):
            raise ValueError(
                "Field measurement_period_delete was not selected as part of the query"
            )
        return self._measurement_period_delete

    @property
    def measurement_period_upsert(self) -> "Union[GQLMeasurementPeriodUpsert, NotFound]":
        if isinstance(self._measurement_period_upsert, Empty):
            raise ValueError(
                "Field measurement_period_upsert was not selected as part of the query"
            )
        return self._measurement_period_upsert

    @property
    def staff_upsert(self) -> "Union[GQLStaffUpsert, NotFound]":
        if isinstance(self._staff_upsert, Empty):
            raise ValueError("Field staff_upsert was not selected as part of the query")
        return self._staff_upsert

    @property
    def staff_codes_upsert(self) -> "Union[GQLStaffCodesUpsert, NotFound]":
        if isinstance(self._staff_codes_upsert, Empty):
            raise ValueError("Field staff_codes_upsert was not selected as part of the query")
        return self._staff_codes_upsert

    @property
    def phase_delete(self) -> "Union[GQLPhaseDelete, NotFound]":
        if isinstance(self._phase_delete, Empty):
            raise ValueError("Field phase_delete was not selected as part of the query")
        return self._phase_delete

    @property
    def phase_update(self) -> "Union[GQLPhaseUpdate, NotFound]":
        if isinstance(self._phase_update, Empty):
            raise ValueError("Field phase_update was not selected as part of the query")
        return self._phase_update

    @property
    def phase_create(self) -> "Union[GQLPhaseCreate, NotFound]":
        if isinstance(self._phase_create, Empty):
            raise ValueError("Field phase_create was not selected as part of the query")
        return self._phase_create

    @property
    def phase_upsert(self) -> "Union[GQLPhaseUpsert, NotFound]":
        if isinstance(self._phase_upsert, Empty):
            raise ValueError("Field phase_upsert was not selected as part of the query")
        return self._phase_upsert

    @property
    def phase_relationship_delete(self) -> "Union[GQLPhaseRelationshipDelete, NotFound]":
        if isinstance(self._phase_relationship_delete, Empty):
            raise ValueError(
                "Field phase_relationship_delete was not selected as part of the query"
            )
        return self._phase_relationship_delete

    @property
    def phase_relationship_create(self) -> "Union[GQLPhaseRelationshipCreate, NotFound]":
        if isinstance(self._phase_relationship_create, Empty):
            raise ValueError(
                "Field phase_relationship_create was not selected as part of the query"
            )
        return self._phase_relationship_create

    @property
    def observation_create(self) -> "Union[GQLObservationCreate, NotFound]":
        if isinstance(self._observation_create, Empty):
            raise ValueError("Field observation_create was not selected as part of the query")
        return self._observation_create

    @property
    def observation_delete(self) -> "Union[GQLObservationDelete, NotFound]":
        if isinstance(self._observation_delete, Empty):
            raise ValueError("Field observation_delete was not selected as part of the query")
        return self._observation_delete

    @property
    def observations_upsert(self) -> "Union[GQLObservationsUpsert, NotFound]":
        if isinstance(self._observations_upsert, Empty):
            raise ValueError("Field observations_upsert was not selected as part of the query")
        return self._observations_upsert

    @property
    def subscribers_upsert(self) -> "Union[GQLSubscribersUpsert, NotFound]":
        if isinstance(self._subscribers_upsert, Empty):
            raise ValueError("Field subscribers_upsert was not selected as part of the query")
        return self._subscribers_upsert

    @property
    def staffing_needs_ratio_create(self) -> "Union[GQLStaffingNeedsRatioCreate, NotFound]":
        if isinstance(self._staffing_needs_ratio_create, Empty):
            raise ValueError(
                "Field staffing_needs_ratio_create was not selected as part of the query"
            )
        return self._staffing_needs_ratio_create

    @property
    def notify_staff_for_events(self) -> "Union[GQLNotifyStaffForEvents, NotFound]":
        if isinstance(self._notify_staff_for_events, Empty):
            raise ValueError("Field notify_staff_for_events was not selected as part of the query")
        return self._notify_staff_for_events

    @property
    def check_notifications_errors(self) -> "Union[GQLCheckNotificationsErrors, NotFound]":
        if isinstance(self._check_notifications_errors, Empty):
            raise ValueError(
                "Field check_notifications_errors was not selected as part of the query"
            )
        return self._check_notifications_errors

    @property
    def email_available_times(self) -> "Union[GQLEmailAvailableTimes, NotFound]":
        if isinstance(self._email_available_times, Empty):
            raise ValueError("Field email_available_times was not selected as part of the query")
        return self._email_available_times

    @property
    def email_available_times_html(self) -> "Union[GQLEmailAvailableTimesHtml, NotFound]":
        if isinstance(self._email_available_times_html, Empty):
            raise ValueError(
                "Field email_available_times_html was not selected as part of the query"
            )
        return self._email_available_times_html

    @property
    def block_create(self) -> "Union[GQLBlockCreate, NotFound]":
        if isinstance(self._block_create, Empty):
            raise ValueError("Field block_create was not selected as part of the query")
        return self._block_create

    @property
    def block_update(self) -> "Union[GQLBlockUpdate, NotFound]":
        if isinstance(self._block_update, Empty):
            raise ValueError("Field block_update was not selected as part of the query")
        return self._block_update

    @property
    def block_archive(self) -> "Union[GQLBlockArchive, NotFound]":
        if isinstance(self._block_archive, Empty):
            raise ValueError("Field block_archive was not selected as part of the query")
        return self._block_archive

    @property
    def block_unarchive(self) -> "Union[GQLBlockUnarchive, NotFound]":
        if isinstance(self._block_unarchive, Empty):
            raise ValueError("Field block_unarchive was not selected as part of the query")
        return self._block_unarchive

    @property
    def block_time_bulk_create(self) -> "Union[GQLBlockTimeBulkCreate, NotFound]":
        if isinstance(self._block_time_bulk_create, Empty):
            raise ValueError("Field block_time_bulk_create was not selected as part of the query")
        return self._block_time_bulk_create

    @property
    def block_releases_reprocess(self) -> "Union[GQLBlockReleaseReprocess, NotFound]":
        if isinstance(self._block_releases_reprocess, Empty):
            raise ValueError("Field block_releases_reprocess was not selected as part of the query")
        return self._block_releases_reprocess

    @property
    def block_release_process_date_range(
        self,
    ) -> "Union[GQLBlockReleaseProcessDateRange, NotFound]":
        if isinstance(self._block_release_process_date_range, Empty):
            raise ValueError(
                "Field block_release_process_date_range was not selected as part of the query"
            )
        return self._block_release_process_date_range

    @property
    def block_release_file_transform(self) -> "Union[GQLBlockReleaseFileTransform, NotFound]":
        if isinstance(self._block_release_file_transform, Empty):
            raise ValueError(
                "Field block_release_file_transform was not selected as part of the query"
            )
        return self._block_release_file_transform

    @property
    def block_time_bulk_delete_duplicate(
        self,
    ) -> "Union[GQLBlockTimeBulkDeleteDuplicate, NotFound]":
        if isinstance(self._block_time_bulk_delete_duplicate, Empty):
            raise ValueError(
                "Field block_time_bulk_delete_duplicate was not selected as part of the query"
            )
        return self._block_time_bulk_delete_duplicate

    @property
    def board_config_create(self) -> "Union[GQLBoardConfigCreate, NotFound]":
        if isinstance(self._board_config_create, Empty):
            raise ValueError("Field board_config_create was not selected as part of the query")
        return self._board_config_create

    @property
    def board_config_update(self) -> "Union[GQLBoardConfigUpdate, NotFound]":
        if isinstance(self._board_config_update, Empty):
            raise ValueError("Field board_config_update was not selected as part of the query")
        return self._board_config_update

    @property
    def board_config_delete(self) -> "Union[GQLBoardConfigDelete, NotFound]":
        if isinstance(self._board_config_delete, Empty):
            raise ValueError("Field board_config_delete was not selected as part of the query")
        return self._board_config_delete

    @property
    def organization_create(self) -> "Union[GQLOrganizationCreate, NotFound]":
        if isinstance(self._organization_create, Empty):
            raise ValueError("Field organization_create was not selected as part of the query")
        return self._organization_create

    @property
    def organization_update(self) -> "Union[GQLOrganizationUpdate, NotFound]":
        if isinstance(self._organization_update, Empty):
            raise ValueError("Field organization_update was not selected as part of the query")
        return self._organization_update

    @property
    def site_create(self) -> "Union[GQLSiteCreate, NotFound]":
        if isinstance(self._site_create, Empty):
            raise ValueError("Field site_create was not selected as part of the query")
        return self._site_create

    @property
    def site_update(self) -> "Union[GQLSiteUpdate, NotFound]":
        if isinstance(self._site_update, Empty):
            raise ValueError("Field site_update was not selected as part of the query")
        return self._site_update

    @property
    def site_prime_time_config_upsert(self) -> "Union[GQLSitePrimeTimeConfigUpsert, NotFound]":
        if isinstance(self._site_prime_time_config_upsert, Empty):
            raise ValueError(
                "Field site_prime_time_config_upsert was not selected as part of the query"
            )
        return self._site_prime_time_config_upsert

    @property
    def site_closure_create(self) -> "Union[GQLSiteClosureCreate, NotFound]":
        if isinstance(self._site_closure_create, Empty):
            raise ValueError("Field site_closure_create was not selected as part of the query")
        return self._site_closure_create

    @property
    def site_closure_delete(self) -> "Union[GQLSiteClosureDelete, NotFound]":
        if isinstance(self._site_closure_delete, Empty):
            raise ValueError("Field site_closure_delete was not selected as part of the query")
        return self._site_closure_delete

    @property
    def default_site_closures_create(self) -> "Union[GQLDefaultSiteClosuresCreate, NotFound]":
        if isinstance(self._default_site_closures_create, Empty):
            raise ValueError(
                "Field default_site_closures_create was not selected as part of the query"
            )
        return self._default_site_closures_create

    @property
    def site_first_case_config_upsert(self) -> "Union[GQLSiteFirstCaseConfigUpsert, NotFound]":
        if isinstance(self._site_first_case_config_upsert, Empty):
            raise ValueError(
                "Field site_first_case_config_upsert was not selected as part of the query"
            )
        return self._site_first_case_config_upsert

    @property
    def site_launches_upsert(self) -> "Union[GQLSiteLaunchesUpsert, NotFound]":
        if isinstance(self._site_launches_upsert, Empty):
            raise ValueError("Field site_launches_upsert was not selected as part of the query")
        return self._site_launches_upsert

    @property
    def room_create(self) -> "Union[GQLRoomCreate, NotFound]":
        if isinstance(self._room_create, Empty):
            raise ValueError("Field room_create was not selected as part of the query")
        return self._room_create

    @property
    def rooms_create(self) -> "Union[GQLRoomsCreate, NotFound]":
        if isinstance(self._rooms_create, Empty):
            raise ValueError("Field rooms_create was not selected as part of the query")
        return self._rooms_create

    @property
    def room_update(self) -> "Union[GQLRoomUpdate, NotFound]":
        if isinstance(self._room_update, Empty):
            raise ValueError("Field room_update was not selected as part of the query")
        return self._room_update

    @property
    def room_update_configuration(self) -> "Union[GQLRoomUpdateConfiguration, NotFound]":
        if isinstance(self._room_update_configuration, Empty):
            raise ValueError(
                "Field room_update_configuration was not selected as part of the query"
            )
        return self._room_update_configuration

    @property
    def room_tag_create(self) -> "Union[GQLRoomTagCreate, NotFound]":
        if isinstance(self._room_tag_create, Empty):
            raise ValueError("Field room_tag_create was not selected as part of the query")
        return self._room_tag_create

    @property
    def room_set_tags(self) -> "Union[GQLRoomSetTags, NotFound]":
        if isinstance(self._room_set_tags, Empty):
            raise ValueError("Field room_set_tags was not selected as part of the query")
        return self._room_set_tags

    @property
    def room_tag_rename(self) -> "Union[GQLRoomTagRename, NotFound]":
        if isinstance(self._room_tag_rename, Empty):
            raise ValueError("Field room_tag_rename was not selected as part of the query")
        return self._room_tag_rename

    @property
    def room_tag_update(self) -> "Union[GQLRoomTagUpdate, NotFound]":
        if isinstance(self._room_tag_update, Empty):
            raise ValueError("Field room_tag_update was not selected as part of the query")
        return self._room_tag_update

    @property
    def room_closure_create(self) -> "Union[GQLRoomClosureCreate, NotFound]":
        if isinstance(self._room_closure_create, Empty):
            raise ValueError("Field room_closure_create was not selected as part of the query")
        return self._room_closure_create

    @property
    def room_closure_delete(self) -> "Union[GQLRoomClosureDelete, NotFound]":
        if isinstance(self._room_closure_delete, Empty):
            raise ValueError("Field room_closure_delete was not selected as part of the query")
        return self._room_closure_delete

    @property
    def room_prime_time_config_upsert(self) -> "Union[GQLRoomPrimeTimeConfigUpsert, NotFound]":
        if isinstance(self._room_prime_time_config_upsert, Empty):
            raise ValueError(
                "Field room_prime_time_config_upsert was not selected as part of the query"
            )
        return self._room_prime_time_config_upsert

    @property
    def room_prime_time_config_delete(self) -> "Union[GQLRoomPrimeTimeConfigDelete, NotFound]":
        if isinstance(self._room_prime_time_config_delete, Empty):
            raise ValueError(
                "Field room_prime_time_config_delete was not selected as part of the query"
            )
        return self._room_prime_time_config_delete

    @property
    def room_first_case_config_upsert(self) -> "Union[GQLRoomFirstCaseConfigUpsert, NotFound]":
        if isinstance(self._room_first_case_config_upsert, Empty):
            raise ValueError(
                "Field room_first_case_config_upsert was not selected as part of the query"
            )
        return self._room_first_case_config_upsert

    @property
    def room_first_case_config_delete(self) -> "Union[GQLRoomFirstCaseConfigDelete, NotFound]":
        if isinstance(self._room_first_case_config_delete, Empty):
            raise ValueError(
                "Field room_first_case_config_delete was not selected as part of the query"
            )
        return self._room_first_case_config_delete

    @property
    def camera_create(self) -> "Union[GQLCameraCreate, NotFound]":
        if isinstance(self._camera_create, Empty):
            raise ValueError("Field camera_create was not selected as part of the query")
        return self._camera_create

    @property
    def cameras_create(self) -> "Union[GQLCamerasCreate, NotFound]":
        if isinstance(self._cameras_create, Empty):
            raise ValueError("Field cameras_create was not selected as part of the query")
        return self._cameras_create

    @property
    def camera_update(self) -> "Union[GQLCameraUpdate, NotFound]":
        if isinstance(self._camera_update, Empty):
            raise ValueError("Field camera_update was not selected as part of the query")
        return self._camera_update

    @property
    def user_filter_view_create(self) -> "Union[GQLUserFilterViewCreate, NotFound]":
        if isinstance(self._user_filter_view_create, Empty):
            raise ValueError("Field user_filter_view_create was not selected as part of the query")
        return self._user_filter_view_create

    @property
    def user_filter_view_update(self) -> "Union[GQLUserFilterViewUpdate, NotFound]":
        if isinstance(self._user_filter_view_update, Empty):
            raise ValueError("Field user_filter_view_update was not selected as part of the query")
        return self._user_filter_view_update

    @property
    def user_filter_view_delete(self) -> "Union[GQLUserFilterViewDelete, NotFound]":
        if isinstance(self._user_filter_view_delete, Empty):
            raise ValueError("Field user_filter_view_delete was not selected as part of the query")
        return self._user_filter_view_delete

    @property
    def cluster_create(self) -> "Union[GQLClusterCreate, NotFound]":
        if isinstance(self._cluster_create, Empty):
            raise ValueError("Field cluster_create was not selected as part of the query")
        return self._cluster_create

    @property
    def cluster_update(self) -> "Union[GQLClusterUpdate, NotFound]":
        if isinstance(self._cluster_update, Empty):
            raise ValueError("Field cluster_update was not selected as part of the query")
        return self._cluster_update

    @property
    def turnover_goals_update(self) -> "Union[GQLTurnoverGoalsUpdate, NotFound]":
        if isinstance(self._turnover_goals_update, Empty):
            raise ValueError("Field turnover_goals_update was not selected as part of the query")
        return self._turnover_goals_update

    @property
    def case_forecast_upsert(self) -> "Union[GQLCaseForecastUpsert, NotFound]":
        if isinstance(self._case_forecast_upsert, Empty):
            raise ValueError("Field case_forecast_upsert was not selected as part of the query")
        return self._case_forecast_upsert

    @property
    def upsert_forecasts_for_cases(self) -> "Union[GQLUpsertForecastsForCases, NotFound]":
        if isinstance(self._upsert_forecasts_for_cases, Empty):
            raise ValueError(
                "Field upsert_forecasts_for_cases was not selected as part of the query"
            )
        return self._upsert_forecasts_for_cases

    @property
    def event_dashboard_visibility_upsert(
        self,
    ) -> "Union[GQLEventDashboardVisibilityUpsert, NotFound]":
        if isinstance(self._event_dashboard_visibility_upsert, Empty):
            raise ValueError(
                "Field event_dashboard_visibility_upsert was not selected as part of the query"
            )
        return self._event_dashboard_visibility_upsert

    @property
    def event_dashboard_visibility_delete(
        self,
    ) -> "Union[GQLEventDashboardVisibilityDelete, NotFound]":
        if isinstance(self._event_dashboard_visibility_delete, Empty):
            raise ValueError(
                "Field event_dashboard_visibility_delete was not selected as part of the query"
            )
        return self._event_dashboard_visibility_delete

    @property
    def terminal_clean_score_upsert(self) -> "Union[GQLTerminalCleanScoreUpsert, NotFound]":
        if isinstance(self._terminal_clean_score_upsert, Empty):
            raise ValueError(
                "Field terminal_clean_score_upsert was not selected as part of the query"
            )
        return self._terminal_clean_score_upsert

    @property
    def turnover_label_note_upsert(self) -> "Union[GQLTurnoverLabelNoteUpsert, NotFound]":
        if isinstance(self._turnover_label_note_upsert, Empty):
            raise ValueError(
                "Field turnover_label_note_upsert was not selected as part of the query"
            )
        return self._turnover_label_note_upsert

    @property
    def case_to_block_overrides_upsert(self) -> "Union[GQLCaseToBlockOverridesUpsert, NotFound]":
        if isinstance(self._case_to_block_overrides_upsert, Empty):
            raise ValueError(
                "Field case_to_block_overrides_upsert was not selected as part of the query"
            )
        return self._case_to_block_overrides_upsert

    @property
    def custom_phase_config_upsert(self) -> "Union[GQLCustomPhaseConfigUpsert, NotFound]":
        if isinstance(self._custom_phase_config_upsert, Empty):
            raise ValueError(
                "Field custom_phase_config_upsert was not selected as part of the query"
            )
        return self._custom_phase_config_upsert

    @property
    def custom_phase_config_delete(self) -> "Union[GQLCustomPhaseConfigDelete, NotFound]":
        if isinstance(self._custom_phase_config_delete, Empty):
            raise ValueError(
                "Field custom_phase_config_delete was not selected as part of the query"
            )
        return self._custom_phase_config_delete

    @property
    def case_label_upsert(self) -> "Union[GQLCaseLabelUpsert, NotFound]":
        if isinstance(self._case_label_upsert, Empty):
            raise ValueError("Field case_label_upsert was not selected as part of the query")
        return self._case_label_upsert


class GQLEventCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_event: "Union[GQLEvent, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_event = created_event

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_event(self) -> "Union[GQLEvent, NotFound]":
        if isinstance(self._created_event, Empty):
            raise ValueError("Field created_event was not selected as part of the query")
        return self._created_event


class GQLEventUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        updated_event: "Union[GQLEvent, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_event = updated_event

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_event(self) -> "Union[GQLEvent, NotFound]":
        if isinstance(self._updated_event, Empty):
            raise ValueError("Field updated_event was not selected as part of the query")
        return self._updated_event


class GQLEventDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLEventUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_events: "Union[list[GQLEvent], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_events = created_events

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_events(self) -> "list[GQLEvent]":
        if isinstance(self._created_events, Empty):
            raise ValueError("Field created_events was not selected as part of the query")
        return self._created_events


class GQLEventTypeCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_event_type: "Union[GQLEventType, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_event_type = created_event_type

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_event_type(self) -> "GQLEventType":
        if isinstance(self._created_event_type, Empty):
            raise ValueError("Field created_event_type was not selected as part of the query")
        return self._created_event_type


class GQLEventTypeUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        updated_event_type: "Union[GQLEventType, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_event_type = updated_event_type

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_event_type(self) -> "GQLEventType":
        if isinstance(self._updated_event_type, Empty):
            raise ValueError("Field updated_event_type was not selected as part of the query")
        return self._updated_event_type


class GQLEventLabelOptionCreate(GQLClientObject):
    def __init__(
        self,
        event_label_option: "Union[GQLEventLabelOption, NotFound, Empty]" = Empty(),
    ) -> None:
        self._event_label_option = event_label_option

    @property
    def event_label_option(self) -> "Union[GQLEventLabelOption, NotFound]":
        if isinstance(self._event_label_option, Empty):
            raise ValueError("Field event_label_option was not selected as part of the query")
        return self._event_label_option


class GQLEventLabelOptionUpdate(GQLClientObject):
    def __init__(
        self,
        event_label_option: "Union[GQLEventLabelOption, NotFound, Empty]" = Empty(),
    ) -> None:
        self._event_label_option = event_label_option

    @property
    def event_label_option(self) -> "Union[GQLEventLabelOption, NotFound]":
        if isinstance(self._event_label_option, Empty):
            raise ValueError("Field event_label_option was not selected as part of the query")
        return self._event_label_option


class GQLEventLabelOptionDelete(GQLClientObject):
    def __init__(
        self,
        id: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._id = id

    @property
    def id(self) -> "Union[str, NotFound]":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id


class GQLHighlightCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_highlight: "Union[GQLHighlight, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_highlight = created_highlight

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_highlight(self) -> "Union[GQLHighlight, NotFound]":
        if isinstance(self._created_highlight, Empty):
            raise ValueError("Field created_highlight was not selected as part of the query")
        return self._created_highlight


class GQLHighlightUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_highlight: "Union[GQLHighlight, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_highlight = updated_highlight

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_highlight(self) -> "Union[GQLHighlight, NotFound]":
        if isinstance(self._updated_highlight, Empty):
            raise ValueError("Field updated_highlight was not selected as part of the query")
        return self._updated_highlight


class GQLHighlightDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLHighlightArchive(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        archived_highlight: "Union[GQLHighlight, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._archived_highlight = archived_highlight

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def archived_highlight(self) -> "Union[GQLHighlight, NotFound]":
        if isinstance(self._archived_highlight, Empty):
            raise ValueError("Field archived_highlight was not selected as part of the query")
        return self._archived_highlight


class GQLHighlightFeedbackCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_highlight_feedback: "Union[GQLHighlightFeedback, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_highlight_feedback = created_highlight_feedback

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_highlight_feedback(self) -> "Union[GQLHighlightFeedback, NotFound]":
        if isinstance(self._created_highlight_feedback, Empty):
            raise ValueError(
                "Field created_highlight_feedback was not selected as part of the query"
            )
        return self._created_highlight_feedback


class GQLHighlightFeedbackUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_highlight_feedback: "Union[GQLHighlightFeedback, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_highlight_feedback = updated_highlight_feedback

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_highlight_feedback(self) -> "Union[GQLHighlightFeedback, NotFound]":
        if isinstance(self._updated_highlight_feedback, Empty):
            raise ValueError(
                "Field updated_highlight_feedback was not selected as part of the query"
            )
        return self._updated_highlight_feedback


class GQLAnnotationTaskBulkGenerate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLAnnotationTaskBulkUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        count: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._count = count

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def count(self) -> "Union[int, NotFound]":
        if isinstance(self._count, Empty):
            raise ValueError("Field count was not selected as part of the query")
        return self._count


class GQLAnnotationTaskUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_annotation_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_annotation_task = updated_annotation_task

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_annotation_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._updated_annotation_task, Empty):
            raise ValueError("Field updated_annotation_task was not selected as part of the query")
        return self._updated_annotation_task


class GQLAnnotationTaskExit(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._task = task

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._task, Empty):
            raise ValueError("Field task was not selected as part of the query")
        return self._task


class GQLAnnnotationTaskTypeCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_annotation_task_type: "Union[GQLAnnotationTaskType, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_annotation_task_type = created_annotation_task_type

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_annotation_task_type(self) -> "GQLAnnotationTaskType":
        if isinstance(self._created_annotation_task_type, Empty):
            raise ValueError(
                "Field created_annotation_task_type was not selected as part of the query"
            )
        return self._created_annotation_task_type


class GQLAnnnotationTaskTypeUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        updated_annotation_task_type: "Union[GQLAnnotationTaskType, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_annotation_task_type = updated_annotation_task_type

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_annotation_task_type(self) -> "GQLAnnotationTaskType":
        if isinstance(self._updated_annotation_task_type, Empty):
            raise ValueError(
                "Field updated_annotation_task_type was not selected as part of the query"
            )
        return self._updated_annotation_task_type


class GQLAnnotationTaskScheduleCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_annotation_task_schedule: "Union[GQLAnnotationTaskSchedule, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_annotation_task_schedule = created_annotation_task_schedule

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_annotation_task_schedule(self) -> "GQLAnnotationTaskSchedule":
        if isinstance(self._created_annotation_task_schedule, Empty):
            raise ValueError(
                "Field created_annotation_task_schedule was not selected as part of the query"
            )
        return self._created_annotation_task_schedule


class GQLAnnotationTaskScheduleUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        updated_annotation_task_schedule: "Union[GQLAnnotationTaskSchedule, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_annotation_task_schedule = updated_annotation_task_schedule

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_annotation_task_schedule(self) -> "GQLAnnotationTaskSchedule":
        if isinstance(self._updated_annotation_task_schedule, Empty):
            raise ValueError(
                "Field updated_annotation_task_schedule was not selected as part of the query"
            )
        return self._updated_annotation_task_schedule


class GQLAnnotationTaskNextAnnotate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        current_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
        next_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._current_task = current_task
        self._next_task = next_task

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def current_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._current_task, Empty):
            raise ValueError("Field current_task was not selected as part of the query")
        return self._current_task

    @property
    def next_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._next_task, Empty):
            raise ValueError("Field next_task was not selected as part of the query")
        return self._next_task


class GQLAnnotationTaskNextReview(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        current_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
        next_task: "Union[GQLAnnotationTask, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._current_task = current_task
        self._next_task = next_task

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def current_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._current_task, Empty):
            raise ValueError("Field current_task was not selected as part of the query")
        return self._current_task

    @property
    def next_task(self) -> "Union[GQLAnnotationTask, NotFound]":
        if isinstance(self._next_task, Empty):
            raise ValueError("Field next_task was not selected as part of the query")
        return self._next_task


class GQLCameraCaptureLatestImage(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseProceduresUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLProcessCaseDerivedProperties(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseNotePlanUpsert(GQLClientObject):
    def __init__(
        self,
        case_note_plan: "Union[GQLCaseNotePlan, Empty]" = Empty(),
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._case_note_plan = case_note_plan
        self._success = success

    @property
    def case_note_plan(self) -> "GQLCaseNotePlan":
        if isinstance(self._case_note_plan, Empty):
            raise ValueError("Field case_note_plan was not selected as part of the query")
        return self._case_note_plan

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseProceduresUpsertAndArchive(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseStaffUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseStaffUpsertAndArchive(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCaseStaffPlanUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        case_staff_plans: "Union[GQLCaseStaffPlanConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._case_staff_plans = case_staff_plans

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def case_staff_plans(self) -> "GQLCaseStaffPlanConnection":
        if isinstance(self._case_staff_plans, Empty):
            raise ValueError("Field case_staff_plans was not selected as part of the query")
        return self._case_staff_plans


class GQLCaseFlagUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        case_flags: "Union[GQLCaseFlagConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._case_flags = case_flags

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def case_flags(self) -> "GQLCaseFlagConnection":
        if isinstance(self._case_flags, Empty):
            raise ValueError("Field case_flags was not selected as part of the query")
        return self._case_flags


class GQLCaseFlagConnection(GQLClientObject):
    def __init__(
        self,
        page_info: "Union[GQLPageInfo, Empty]" = Empty(),
        edges: "Union[list[GQLCaseFlagEdge], Empty]" = Empty(),
        page_cursors: "Union[GQLPageCursors, NotFound, Empty]" = Empty(),
        total_records: "Union[int, NotFound, Empty]" = Empty(),
    ) -> None:
        self._page_info = page_info
        self._edges = edges
        self._page_cursors = page_cursors
        self._total_records = total_records

    @property
    def page_info(self) -> "GQLPageInfo":
        if isinstance(self._page_info, Empty):
            raise ValueError("Field page_info was not selected as part of the query")
        return self._page_info

    @property
    def edges(self) -> "list[GQLCaseFlagEdge]":
        if isinstance(self._edges, Empty):
            raise ValueError("Field edges was not selected as part of the query")
        return self._edges

    @property
    def page_cursors(self) -> "Union[GQLPageCursors, NotFound]":
        if isinstance(self._page_cursors, Empty):
            raise ValueError("Field page_cursors was not selected as part of the query")
        return self._page_cursors

    @property
    def total_records(self) -> "Union[int, NotFound]":
        if isinstance(self._total_records, Empty):
            raise ValueError("Field total_records was not selected as part of the query")
        return self._total_records


class GQLCaseFlagEdge(GQLClientObject):
    def __init__(
        self,
        node: "Union[GQLCaseFlag, Empty]" = Empty(),
        cursor: "Union[str, Empty]" = Empty(),
    ) -> None:
        self._node = node
        self._cursor = cursor

    @property
    def node(self) -> "GQLCaseFlag":
        if isinstance(self._node, Empty):
            raise ValueError("Field node was not selected as part of the query")
        return self._node

    @property
    def cursor(self) -> "str":
        if isinstance(self._cursor, Empty):
            raise ValueError("Field cursor was not selected as part of the query")
        return self._cursor


class GQLMatchCases(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        phases: "Union[Union[list[GQLPhase], NotFound], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._phases = phases

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def phases(self) -> "Union[Union[list[GQLPhase], NotFound]]":
        if isinstance(self._phases, Empty):
            raise ValueError("Field phases was not selected as part of the query")
        return self._phases


class GQLProceduresUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_procedures: "Union[GQLProcedureConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_procedures = created_procedures

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_procedures(self) -> "GQLProcedureConnection":
        if isinstance(self._created_procedures, Empty):
            raise ValueError("Field created_procedures was not selected as part of the query")
        return self._created_procedures


class GQLAnesthesiasUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        anesthesias: "Union[GQLAnesthesiaConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._anesthesias = anesthesias

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def anesthesias(self) -> "GQLAnesthesiaConnection":
        if isinstance(self._anesthesias, Empty):
            raise ValueError("Field anesthesias was not selected as part of the query")
        return self._anesthesias


class GQLMeasurementPeriodDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLMeasurementPeriodUpsert(GQLClientObject):
    def __init__(
        self,
        measurement_period: "Union[GQLMeasurementPeriod, NotFound, Empty]" = Empty(),
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._measurement_period = measurement_period
        self._success = success

    @property
    def measurement_period(self) -> "Union[GQLMeasurementPeriod, NotFound]":
        if isinstance(self._measurement_period, Empty):
            raise ValueError("Field measurement_period was not selected as part of the query")
        return self._measurement_period

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLStaffUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_staff: "Union[GQLStaffConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_staff = created_staff

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_staff(self) -> "GQLStaffConnection":
        if isinstance(self._created_staff, Empty):
            raise ValueError("Field created_staff was not selected as part of the query")
        return self._created_staff


class GQLStaffCodesUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_staff_codes: "Union[list[GQLStaffCode], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_staff_codes = created_staff_codes

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_staff_codes(self) -> "list[GQLStaffCode]":
        if isinstance(self._created_staff_codes, Empty):
            raise ValueError("Field created_staff_codes was not selected as part of the query")
        return self._created_staff_codes


class GQLPhaseDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLPhaseUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLPhaseCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_phase: "Union[GQLPhase, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_phase = created_phase

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_phase(self) -> "GQLPhase":
        if isinstance(self._created_phase, Empty):
            raise ValueError("Field created_phase was not selected as part of the query")
        return self._created_phase


class GQLPhaseUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_phases: "Union[list[GQLPhase], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_phases = created_phases

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_phases(self) -> "list[GQLPhase]":
        if isinstance(self._created_phases, Empty):
            raise ValueError("Field created_phases was not selected as part of the query")
        return self._created_phases


class GQLPhaseRelationshipDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLPhaseRelationshipCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLObservationCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_observation: "Union[GQLObservation, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_observation = created_observation

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_observation(self) -> "Union[GQLObservation, NotFound]":
        if isinstance(self._created_observation, Empty):
            raise ValueError("Field created_observation was not selected as part of the query")
        return self._created_observation


class GQLObservationDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLObservationsUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_observations: "Union[GQLObservationConnection, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_observations = created_observations

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_observations(self) -> "GQLObservationConnection":
        if isinstance(self._created_observations, Empty):
            raise ValueError("Field created_observations was not selected as part of the query")
        return self._created_observations


class GQLSubscribersUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        contact_information: "Union[list[GQLContactInformation], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._contact_information = contact_information

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def contact_information(self) -> "list[GQLContactInformation]":
        if isinstance(self._contact_information, Empty):
            raise ValueError("Field contact_information was not selected as part of the query")
        return self._contact_information


class GQLStaffingNeedsRatioCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_staffing_needs_ratio: "Union[GQLStaffingNeedsRatio, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_staffing_needs_ratio = created_staffing_needs_ratio

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_staffing_needs_ratio(self) -> "Union[GQLStaffingNeedsRatio, NotFound]":
        if isinstance(self._created_staffing_needs_ratio, Empty):
            raise ValueError(
                "Field created_staffing_needs_ratio was not selected as part of the query"
            )
        return self._created_staffing_needs_ratio


class GQLNotifyStaffForEvents(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        sent_count: "Union[int, Empty]" = Empty(),
        failed_event_ids: "Union[list[str], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._sent_count = sent_count
        self._failed_event_ids = failed_event_ids

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def sent_count(self) -> "int":
        if isinstance(self._sent_count, Empty):
            raise ValueError("Field sent_count was not selected as part of the query")
        return self._sent_count

    @property
    def failed_event_ids(self) -> "list[str]":
        if isinstance(self._failed_event_ids, Empty):
            raise ValueError("Field failed_event_ids was not selected as part of the query")
        return self._failed_event_ids


class GQLCheckNotificationsErrors(GQLClientObject):
    def __init__(
        self,
        excess_notifications: "Union[list[GQLEventNotification], Empty]" = Empty(),
        missing_notifications: "Union[list[GQLMissingNotification], Empty]" = Empty(),
        duplicate_notifications: "Union[list[GQLEventNotification], Empty]" = Empty(),
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._excess_notifications = excess_notifications
        self._missing_notifications = missing_notifications
        self._duplicate_notifications = duplicate_notifications
        self._success = success

    @property
    def excess_notifications(self) -> "list[GQLEventNotification]":
        if isinstance(self._excess_notifications, Empty):
            raise ValueError("Field excess_notifications was not selected as part of the query")
        return self._excess_notifications

    @property
    def missing_notifications(self) -> "list[GQLMissingNotification]":
        if isinstance(self._missing_notifications, Empty):
            raise ValueError("Field missing_notifications was not selected as part of the query")
        return self._missing_notifications

    @property
    def duplicate_notifications(self) -> "list[GQLEventNotification]":
        if isinstance(self._duplicate_notifications, Empty):
            raise ValueError("Field duplicate_notifications was not selected as part of the query")
        return self._duplicate_notifications

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLMissingNotification(GQLClientObject):
    def __init__(
        self,
        event: "Union[GQLEvent, Empty]" = Empty(),
        subscriber_information: "Union[GQLStaffEventNotificationContactInformation, Empty]" = Empty(),
        case: "Union[GQLScheduledCase, NotFound, Empty]" = Empty(),
    ) -> None:
        self._event = event
        self._subscriber_information = subscriber_information
        self._case = case

    @property
    def event(self) -> "GQLEvent":
        if isinstance(self._event, Empty):
            raise ValueError("Field event was not selected as part of the query")
        return self._event

    @property
    def subscriber_information(self) -> "GQLStaffEventNotificationContactInformation":
        if isinstance(self._subscriber_information, Empty):
            raise ValueError("Field subscriber_information was not selected as part of the query")
        return self._subscriber_information

    @property
    def case(self) -> "Union[GQLScheduledCase, NotFound]":
        if isinstance(self._case, Empty):
            raise ValueError("Field case was not selected as part of the query")
        return self._case


class GQLEmailAvailableTimes(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLEmailAvailableTimesHtml(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        email: "Union[str, NotFound, Empty]" = Empty(),
        subject: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._email = email
        self._subject = subject

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def email(self) -> "Union[str, NotFound]":
        if isinstance(self._email, Empty):
            raise ValueError("Field email was not selected as part of the query")
        return self._email

    @property
    def subject(self) -> "Union[str, NotFound]":
        if isinstance(self._subject, Empty):
            raise ValueError("Field subject was not selected as part of the query")
        return self._subject


class GQLBlockCreate(GQLClientObject):
    def __init__(
        self,
        block: "Union[GQLBlock, NotFound, Empty]" = Empty(),
    ) -> None:
        self._block = block

    @property
    def block(self) -> "Union[GQLBlock, NotFound]":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block


class GQLBlockUpdate(GQLClientObject):
    def __init__(
        self,
        block: "Union[GQLBlock, NotFound, Empty]" = Empty(),
    ) -> None:
        self._block = block

    @property
    def block(self) -> "Union[GQLBlock, NotFound]":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block


class GQLBlockArchive(GQLClientObject):
    def __init__(
        self,
        block: "Union[GQLBlock, NotFound, Empty]" = Empty(),
    ) -> None:
        self._block = block

    @property
    def block(self) -> "Union[GQLBlock, NotFound]":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block


class GQLBlockUnarchive(GQLClientObject):
    def __init__(
        self,
        block: "Union[GQLBlock, NotFound, Empty]" = Empty(),
    ) -> None:
        self._block = block

    @property
    def block(self) -> "Union[GQLBlock, NotFound]":
        if isinstance(self._block, Empty):
            raise ValueError("Field block was not selected as part of the query")
        return self._block


class GQLBlockTimeBulkCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        ids: "Union[list[str], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._ids = ids

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def ids(self) -> "list[str]":
        if isinstance(self._ids, Empty):
            raise ValueError("Field ids was not selected as part of the query")
        return self._ids


class GQLBlockReleaseReprocess(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLBlockReleaseProcessDateRange(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLBlockReleaseFileTransform(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLBlockTimeBulkDeleteDuplicate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        deleted_count: "Union[int, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._deleted_count = deleted_count

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def deleted_count(self) -> "int":
        if isinstance(self._deleted_count, Empty):
            raise ValueError("Field deleted_count was not selected as part of the query")
        return self._deleted_count


class GQLBoardConfigCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        board_config: "Union[GQLBoardConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._board_config = board_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def board_config(self) -> "Union[GQLBoardConfig, NotFound]":
        if isinstance(self._board_config, Empty):
            raise ValueError("Field board_config was not selected as part of the query")
        return self._board_config


class GQLBoardConfigUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        board_config: "Union[GQLBoardConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._board_config = board_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def board_config(self) -> "Union[GQLBoardConfig, NotFound]":
        if isinstance(self._board_config, Empty):
            raise ValueError("Field board_config was not selected as part of the query")
        return self._board_config


class GQLBoardConfigDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLOrganizationCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_organization: "Union[GQLOrganization, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_organization = created_organization

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_organization(self) -> "Union[GQLOrganization, NotFound]":
        if isinstance(self._created_organization, Empty):
            raise ValueError("Field created_organization was not selected as part of the query")
        return self._created_organization


class GQLOrganizationUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_organization: "Union[GQLOrganization, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_organization = updated_organization

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_organization(self) -> "Union[GQLOrganization, NotFound]":
        if isinstance(self._updated_organization, Empty):
            raise ValueError("Field updated_organization was not selected as part of the query")
        return self._updated_organization


class GQLSiteCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_site: "Union[GQLSite, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_site = created_site

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_site(self) -> "Union[GQLSite, NotFound]":
        if isinstance(self._created_site, Empty):
            raise ValueError("Field created_site was not selected as part of the query")
        return self._created_site


class GQLSiteUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_site: "Union[GQLSite, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_site = updated_site

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_site(self) -> "Union[GQLSite, NotFound]":
        if isinstance(self._updated_site, Empty):
            raise ValueError("Field updated_site was not selected as part of the query")
        return self._updated_site


class GQLSitePrimeTimeConfigUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        site_prime_time_config: "Union[GQLSitePrimeTimeConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._site = site
        self._site_prime_time_config = site_prime_time_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def site_prime_time_config(self) -> "Union[GQLSitePrimeTimeConfig, NotFound]":
        if isinstance(self._site_prime_time_config, Empty):
            raise ValueError("Field site_prime_time_config was not selected as part of the query")
        return self._site_prime_time_config


class GQLSiteClosureCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        created_site_closure: "Union[GQLSiteClosure, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._site = site
        self._created_site_closure = created_site_closure

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def created_site_closure(self) -> "Union[GQLSiteClosure, NotFound]":
        if isinstance(self._created_site_closure, Empty):
            raise ValueError("Field created_site_closure was not selected as part of the query")
        return self._created_site_closure


class GQLSiteClosureDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._site = site

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site


class GQLDefaultSiteClosuresCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_site_closures: "Union[Union[list[Union[GQLSiteClosure, Empty]], NotFound], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_site_closures = created_site_closures

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_site_closures(self) -> "Union[Union[list[Union[GQLSiteClosure, Empty]], NotFound]]":
        if isinstance(self._created_site_closures, Empty):
            raise ValueError("Field created_site_closures was not selected as part of the query")
        return self._created_site_closures


class GQLSiteFirstCaseConfigUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        site: "Union[GQLSite, Empty]" = Empty(),
        first_case_config: "Union[GQLSiteFirstCaseConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._site = site
        self._first_case_config = first_case_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def site(self) -> "GQLSite":
        if isinstance(self._site, Empty):
            raise ValueError("Field site was not selected as part of the query")
        return self._site

    @property
    def first_case_config(self) -> "Union[GQLSiteFirstCaseConfig, NotFound]":
        if isinstance(self._first_case_config, Empty):
            raise ValueError("Field first_case_config was not selected as part of the query")
        return self._first_case_config


class GQLSiteLaunchesUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLRoomCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_room: "Union[GQLRoom, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_room = created_room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_room(self) -> "Union[GQLRoom, NotFound]":
        if isinstance(self._created_room, Empty):
            raise ValueError("Field created_room was not selected as part of the query")
        return self._created_room


class GQLRoomsCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_rooms: "Union[Union[list[Union[GQLRoom, Empty]], NotFound], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_rooms = created_rooms

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_rooms(self) -> "Union[Union[list[Union[GQLRoom, Empty]], NotFound]]":
        if isinstance(self._created_rooms, Empty):
            raise ValueError("Field created_rooms was not selected as part of the query")
        return self._created_rooms


class GQLRoomUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_room: "Union[GQLRoom, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_room = updated_room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_room(self) -> "Union[GQLRoom, NotFound]":
        if isinstance(self._updated_room, Empty):
            raise ValueError("Field updated_room was not selected as part of the query")
        return self._updated_room


class GQLRoomUpdateConfiguration(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_room: "Union[GQLRoom, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_room = updated_room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_room(self) -> "Union[GQLRoom, NotFound]":
        if isinstance(self._updated_room, Empty):
            raise ValueError("Field updated_room was not selected as part of the query")
        return self._updated_room


class GQLRoomTagCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_room_tag: "Union[GQLRoomTag, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_room_tag = created_room_tag

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_room_tag(self) -> "Union[GQLRoomTag, NotFound]":
        if isinstance(self._created_room_tag, Empty):
            raise ValueError("Field created_room_tag was not selected as part of the query")
        return self._created_room_tag


class GQLRoomSetTags(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "Union[GQLRoom, NotFound]":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room


class GQLRoomTagRename(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_room_tag: "Union[GQLRoomTag, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_room_tag = updated_room_tag

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_room_tag(self) -> "Union[GQLRoomTag, NotFound]":
        if isinstance(self._updated_room_tag, Empty):
            raise ValueError("Field updated_room_tag was not selected as part of the query")
        return self._updated_room_tag


class GQLRoomTagUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_room_tag: "Union[GQLRoomTag, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_room_tag = updated_room_tag

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_room_tag(self) -> "Union[GQLRoomTag, NotFound]":
        if isinstance(self._updated_room_tag, Empty):
            raise ValueError("Field updated_room_tag was not selected as part of the query")
        return self._updated_room_tag


class GQLRoomClosureCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        created_room_closure: "Union[GQLRoomClosure, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room
        self._created_room_closure = created_room_closure

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def created_room_closure(self) -> "Union[GQLRoomClosure, NotFound]":
        if isinstance(self._created_room_closure, Empty):
            raise ValueError("Field created_room_closure was not selected as part of the query")
        return self._created_room_closure


class GQLRoomClosureDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room


class GQLRoomPrimeTimeConfigUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        room_prime_time_config: "Union[GQLRoomPrimeTimeConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room
        self._room_prime_time_config = room_prime_time_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def room_prime_time_config(self) -> "Union[GQLRoomPrimeTimeConfig, NotFound]":
        if isinstance(self._room_prime_time_config, Empty):
            raise ValueError("Field room_prime_time_config was not selected as part of the query")
        return self._room_prime_time_config


class GQLRoomPrimeTimeConfigDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room


class GQLRoomFirstCaseConfigUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
        first_case_config: "Union[GQLRoomFirstCaseConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room
        self._first_case_config = first_case_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room

    @property
    def first_case_config(self) -> "Union[GQLRoomFirstCaseConfig, NotFound]":
        if isinstance(self._first_case_config, Empty):
            raise ValueError("Field first_case_config was not selected as part of the query")
        return self._first_case_config


class GQLRoomFirstCaseConfigDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        room: "Union[GQLRoom, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._room = room

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def room(self) -> "GQLRoom":
        if isinstance(self._room, Empty):
            raise ValueError("Field room was not selected as part of the query")
        return self._room


class GQLCameraCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_camera: "Union[GQLCamera, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_camera = created_camera

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_camera(self) -> "Union[GQLCamera, NotFound]":
        if isinstance(self._created_camera, Empty):
            raise ValueError("Field created_camera was not selected as part of the query")
        return self._created_camera


class GQLCamerasCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        created_cameras: "Union[Union[list[Union[GQLCamera, Empty]], NotFound], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_cameras = created_cameras

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_cameras(self) -> "Union[Union[list[Union[GQLCamera, Empty]], NotFound]]":
        if isinstance(self._created_cameras, Empty):
            raise ValueError("Field created_cameras was not selected as part of the query")
        return self._created_cameras


class GQLCameraUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_camera: "Union[GQLCamera, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_camera = updated_camera

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_camera(self) -> "Union[GQLCamera, NotFound]":
        if isinstance(self._updated_camera, Empty):
            raise ValueError("Field updated_camera was not selected as part of the query")
        return self._updated_camera


class GQLUserFilterViewCreate(GQLClientObject):
    def __init__(
        self,
        user_filter_view: "Union[GQLUserFilterView, NotFound, Empty]" = Empty(),
    ) -> None:
        self._user_filter_view = user_filter_view

    @property
    def user_filter_view(self) -> "Union[GQLUserFilterView, NotFound]":
        if isinstance(self._user_filter_view, Empty):
            raise ValueError("Field user_filter_view was not selected as part of the query")
        return self._user_filter_view


class GQLUserFilterViewUpdate(GQLClientObject):
    def __init__(
        self,
        user_filter_view: "Union[GQLUserFilterView, NotFound, Empty]" = Empty(),
    ) -> None:
        self._user_filter_view = user_filter_view

    @property
    def user_filter_view(self) -> "Union[GQLUserFilterView, NotFound]":
        if isinstance(self._user_filter_view, Empty):
            raise ValueError("Field user_filter_view was not selected as part of the query")
        return self._user_filter_view


class GQLUserFilterViewDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLClusterCreate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        cluster: "Union[GQLCluster, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._cluster = cluster

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def cluster(self) -> "Union[GQLCluster, NotFound]":
        if isinstance(self._cluster, Empty):
            raise ValueError("Field cluster was not selected as part of the query")
        return self._cluster


class GQLClusterUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        cluster: "Union[GQLCluster, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._cluster = cluster

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def cluster(self) -> "Union[GQLCluster, NotFound]":
        if isinstance(self._cluster, Empty):
            raise ValueError("Field cluster was not selected as part of the query")
        return self._cluster


class GQLTurnoverGoalsUpdate(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        updated_turnover_goals: "Union[GQLTurnoverGoals, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._updated_turnover_goals = updated_turnover_goals

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def updated_turnover_goals(self) -> "Union[GQLTurnoverGoals, NotFound]":
        if isinstance(self._updated_turnover_goals, Empty):
            raise ValueError("Field updated_turnover_goals was not selected as part of the query")
        return self._updated_turnover_goals


class GQLCaseForecastUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_forecasts: "Union[list[GQLCaseForecast], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_forecasts = created_forecasts

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_forecasts(self) -> "list[GQLCaseForecast]":
        if isinstance(self._created_forecasts, Empty):
            raise ValueError("Field created_forecasts was not selected as part of the query")
        return self._created_forecasts


class GQLUpsertForecastsForCases(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
        created_forecasts: "Union[list[GQLCaseForecast], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._created_forecasts = created_forecasts

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def created_forecasts(self) -> "list[GQLCaseForecast]":
        if isinstance(self._created_forecasts, Empty):
            raise ValueError("Field created_forecasts was not selected as part of the query")
        return self._created_forecasts


class GQLEventDashboardVisibilityUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLEventDashboardVisibilityDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "bool":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLTerminalCleanScoreUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        terminal_clean_score: "Union[GQLTerminalCleanScore, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._terminal_clean_score = terminal_clean_score

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def terminal_clean_score(self) -> "Union[GQLTerminalCleanScore, NotFound]":
        if isinstance(self._terminal_clean_score, Empty):
            raise ValueError("Field terminal_clean_score was not selected as part of the query")
        return self._terminal_clean_score


class GQLTurnoverLabelNoteUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        id: "Union[str, Empty]" = Empty(),
        note: "Union[str, NotFound, Empty]" = Empty(),
        label_ids: "Union[Union[list[str], NotFound], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._id = id
        self._note = note
        self._label_ids = label_ids

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def id(self) -> "str":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id

    @property
    def note(self) -> "Union[str, NotFound]":
        if isinstance(self._note, Empty):
            raise ValueError("Field note was not selected as part of the query")
        return self._note

    @property
    def label_ids(self) -> "Union[Union[list[str], NotFound]]":
        if isinstance(self._label_ids, Empty):
            raise ValueError("Field label_ids was not selected as part of the query")
        return self._label_ids


class GQLCaseToBlockOverridesUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success


class GQLCustomPhaseConfigUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        custom_phase_config: "Union[GQLCustomPhaseConfig, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._custom_phase_config = custom_phase_config

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def custom_phase_config(self) -> "Union[GQLCustomPhaseConfig, NotFound]":
        if isinstance(self._custom_phase_config, Empty):
            raise ValueError("Field custom_phase_config was not selected as part of the query")
        return self._custom_phase_config


class GQLCustomPhaseConfigDelete(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        id: "Union[str, NotFound, Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._id = id

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def id(self) -> "Union[str, NotFound]":
        if isinstance(self._id, Empty):
            raise ValueError("Field id was not selected as part of the query")
        return self._id


class GQLCaseLabelUpsert(GQLClientObject):
    def __init__(
        self,
        success: "Union[bool, NotFound, Empty]" = Empty(),
        case_labels: "Union[list[GQLCaseLabel], Empty]" = Empty(),
    ) -> None:
        self._success = success
        self._case_labels = case_labels

    @property
    def success(self) -> "Union[bool, NotFound]":
        if isinstance(self._success, Empty):
            raise ValueError("Field success was not selected as part of the query")
        return self._success

    @property
    def case_labels(self) -> "list[GQLCaseLabel]":
        if isinstance(self._case_labels, Empty):
            raise ValueError("Field case_labels was not selected as part of the query")
        return self._case_labels


class GQLDirection(enum.Enum):
    ASC = enum.auto()
    DESC = enum.auto()


class GQLRoomStatusName(enum.Enum):
    IN_CASE = enum.auto()
    IDLE = enum.auto()
    CLOSED = enum.auto()
    TURNOVER = enum.auto()


class GQLContactInformationType(enum.Enum):
    PHONE_NUMBER = enum.auto()


class GQLEventMatchingStatus(enum.Enum):
    AUTOMATIC = enum.auto()
    OVERRIDE = enum.auto()


class GQLAdministrativeSexType(enum.Enum):
    AMBIGUOUS = enum.auto()
    FEMALE = enum.auto()
    MALE = enum.auto()
    NOT_APPLICABLE = enum.auto()
    OTHER = enum.auto()
    UNKNOWN = enum.auto()


class GQLPatientClass(enum.Enum):
    EMERGENCY = enum.auto()
    HOSPITAL_OUTPATIENT_SURGERY = enum.auto()
    INPATIENT = enum.auto()
    SURGERY_ADMIT = enum.auto()
    PRE_ADMIT = enum.auto()
    OBSERVATION = enum.auto()
    OTHER = enum.auto()


class GQLCaseMatchingStatus(enum.Enum):
    AUTOMATIC = enum.auto()
    OVERRIDE = enum.auto()
    CANCELED = enum.auto()
    NOT_A_CASE = enum.auto()


class GQLPhaseType(enum.Enum):
    TURNOVER = enum.auto()
    TURNOVER_CLEAN = enum.auto()
    TURNOVER_CLEAN_V2 = enum.auto()
    TURNOVER_CLEANED = enum.auto()
    TURNOVER_OPEN = enum.auto()
    DRAPE_DRAPE_TURNOVER = enum.auto()
    PRE_OPERATIVE = enum.auto()
    POST_OPERATIVE = enum.auto()
    CASE = enum.auto()
    TERMINAL_CLEAN = enum.auto()
    INTRA_OPERATIVE = enum.auto()
    ANESTHESIA_PROCEDURE = enum.auto()
    ANESTHESIA_PREP = enum.auto()


class GQLPhaseStatus(enum.Enum):
    INVALID = enum.auto()
    VALID = enum.auto()


class GQLCaseType(enum.Enum):
    LIVE = enum.auto()
    FORECAST = enum.auto()
    COMPLETE = enum.auto()


class GQLCaseStatusName(enum.Enum):
    SCHEDULED = enum.auto()
    IN_FACILITY = enum.auto()
    PRE_PROCEDURE = enum.auto()
    PRE_PROCEDURE_COMPLETE = enum.auto()
    IN_HOLD = enum.auto()
    PREP = enum.auto()
    SURGERY = enum.auto()
    WRAP_UP = enum.auto()
    ACTUAL = enum.auto()
    RECOVERY = enum.auto()
    PHASE_II = enum.auto()
    COMPLETE = enum.auto()


class GQLCaseSource(enum.Enum):
    INTERNAL = enum.auto()
    EXTERNAL = enum.auto()


class GQLTurnoverType(enum.Enum):
    LIVE = enum.auto()
    FORECAST = enum.auto()
    COMPLETE = enum.auto()


class GQLTurnoverStatusName(enum.Enum):
    CLEANING = enum.auto()
    CLEANED = enum.auto()
    OPENING = enum.auto()


class GQLPrimeTimeConfigSource(enum.Enum):
    ROOM = enum.auto()
    SITE = enum.auto()
    DEFAULT = enum.auto()


class GQLFirstCaseConfigSource(enum.Enum):
    ROOM = enum.auto()
    SITE = enum.auto()
    DEFAULT = enum.auto()


class GQLSitePrimeTimeConfigSource(enum.Enum):
    SITE = enum.auto()
    DEFAULT = enum.auto()


class GQLSiteFirstCaseConfigSource(enum.Enum):
    SITE = enum.auto()
    DEFAULT = enum.auto()


class GQLCaseLabelFieldType(enum.Enum):
    BOOLEAN = enum.auto()
    SINGLE_SELECT = enum.auto()


class GQLFeedbackStatus(enum.Enum):
    COMPLETE = enum.auto()
    INCOMPLETE = enum.auto()


class GQLTaskStatus(enum.Enum):
    NOT_STARTED = enum.auto()
    IN_PROGRESS = enum.auto()
    READY_FOR_REVIEW = enum.auto()
    IN_REVIEW = enum.auto()
    DONE = enum.auto()
    CANCELLED = enum.auto()
    BLOCKED = enum.auto()


class GQLCancelledReason(enum.Enum):
    IDLE = enum.auto()
    OUTAGE = enum.auto()
    SENSITIVE_CONTENT = enum.auto()
    STAFF_IN_TRAINING = enum.auto()
    CAMERAS_OUT_OF_SYNC = enum.auto()
    BLOCKED_CAMERAS = enum.auto()
    SKIP = enum.auto()


class GQLDayOfWeek(enum.Enum):
    MONDAY = enum.auto()
    TUESDAY = enum.auto()
    WEDNESDAY = enum.auto()
    THURSDAY = enum.auto()
    FRIDAY = enum.auto()
    SATURDAY = enum.auto()
    SUNDAY = enum.auto()


class GQLCaseStaffRole(enum.Enum):
    PRIMARY_SURGEON = enum.auto()
    ANESTHESIA = enum.auto()
    ALL_ANESTHESIA = enum.auto()
    CIRCULATOR = enum.auto()
    SCRUB_TECH = enum.auto()


class GQLBoardViewType(enum.Enum):
    TIMELINE = enum.auto()
    TILE = enum.auto()


class GQLCaseForecastStatus(enum.Enum):
    INVALID = enum.auto()
    VALID = enum.auto()


class GQLCleanScoreEnum(enum.Enum):
    COMPLETE = enum.auto()
    PARTIAL = enum.auto()
    MISSED = enum.auto()
