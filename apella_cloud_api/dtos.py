import dataclasses
import uuid
from dataclasses import dataclass
from datetime import datetime, date, timedelta
from enum import Enum
from typing import Any, List, Optional, Union
from uuid import UUID

from dataclasses_json import DataClassJsonMixin
from marshmallow import fields

from apella_cloud_api.dataclass import (
    datetime_field,
    field,
    timedelta_field,
    uuid_field,
)


@dataclass
class UserDto(DataClassJsonMixin):
    user_id: Optional[str] = None
    display_name: Optional[str] = None
    email: Optional[str] = field(marshmallow_field=fields.Email())


@dataclass
class CaseInfoDto(DataClassJsonMixin):
    external_service_line_id: Optional[str] = None
    scheduled_start_time: Optional[datetime] = datetime_field()
    scheduled_end_time: Optional[datetime] = datetime_field()
    external_case_id: Optional[str] = None
    case_id: Optional[str] = None
    room_id: Optional[str] = None
    organization_id: Optional[str] = None
    site_id: Optional[str] = None
    status: Optional[str] = None
    case_classification_types_id: Optional[str] = None
    is_add_on: Optional[bool] = None
    patient_class: Optional[str] = None
    cancellation_reason: Optional[List[str]] = None
    latest_processed_message_id: Optional[str] = None


@dataclass
class CaseUpdateDto(DataClassJsonMixin):
    case: CaseInfoDto
    external_message_id: str


@dataclass
class CaseRawResult(DataClassJsonMixin):
    raw: Optional[List[Union[dict[str, Any], str]]] = None


@dataclass
class CaseRawDto(DataClassJsonMixin):
    raw: Union[dict[str, Any], str]
    organization_id: str
    raw_id: Optional[str] = None
    event_type: Optional[str] = None
    external_message_id: Optional[str] = None
    event_time: Optional[datetime] = datetime_field()
    case_id: Optional[str] = None
    external_case_id: Optional[str] = None
    archived_at: Optional[datetime] = None


@dataclass
class CaseSearchDto(DataClassJsonMixin):
    organization_id: Optional[str] = None
    site_id: Optional[str] = None
    site_ids: Optional[List[str]] = None
    room_id: Optional[str] = None
    room_ids: Optional[List[str]] = None
    min_start_time: Optional[datetime] = datetime_field()
    max_start_time: Optional[datetime] = datetime_field()
    min_end_time: Optional[datetime] = datetime_field()
    max_end_time: Optional[datetime] = datetime_field()
    status: Optional[str] = None
    case_classification_types_ids: Optional[List[str]] = None
    min_updated_time: Optional[datetime] = datetime_field()
    max_updated_time: Optional[datetime] = datetime_field()
    min_created_time: Optional[datetime] = datetime_field()
    max_created_time: Optional[datetime] = datetime_field()
    staff_ids: Optional[List[str]] = None
    procedure_ids: Optional[List[str]] = None
    is_add_ons: Optional[List[Optional[bool]]] = None
    case_ids: Optional[List[str]] = None
    case_flags: Optional[List[str]] = None
    matching_statuses: Optional[list[str]] = None


@dataclass
class CaseSearchResultDto(DataClassJsonMixin):
    cases: Optional[List[CaseInfoDto]] = None


@dataclass
class RoomInfoDto(DataClassJsonMixin):
    room_id: Optional[str] = None
    room_name: Optional[str] = None
    site_id: Optional[str] = None
    organization_id: Optional[str] = None
    cameras: Optional[List[str]] = None
    default_camera_id: Optional[str] = None
    privacy_enabled_at: Optional[datetime] = datetime_field()
    privacy_updated_by_user_id: Optional[str] = None
    is_forecasting_enabled: Optional[bool] = True


@dataclass
class SiteInfoDto(DataClassJsonMixin):
    organization_id: str
    site_id: str
    site_name: str
    timezone: str
    rooms: Optional[List[str]] = None


@dataclass
class OrganizationInfoDto(DataClassJsonMixin):
    organization_id: Optional[str] = None
    organization_name: Optional[str] = None


@dataclass
class CameraInfoDto(DataClassJsonMixin):
    camera_id: str
    camera_name: str
    organization_id: str
    site_id: str
    room_id: str


@dataclass
class ScheduleDto(DataClassJsonMixin):
    cases: Optional[List[CaseInfoDto]] = None


@dataclass
class EventDto(DataClassJsonMixin):
    event_id: Optional[str] = None
    event_type: Optional[str] = None
    event_name: Optional[str] = None

    start_time: Optional[datetime] = datetime_field()
    process_timestamp: Optional[datetime] = datetime_field()

    org_id: str = field(serialized_name="organization_id")
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    camera_id: Optional[str] = None

    source: Optional[str] = None
    source_type: Optional[str] = None
    model_version: Optional[Optional[str]] = None
    confidence: Optional[float] = None

    labels: Optional[List[str]] = None
    notes: Optional[Optional[str]] = None


@dataclass
class EventQueryDto(DataClassJsonMixin):
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)
    organization_id: Optional[str] = None
    site_id: Optional[str] = None
    site_ids: Optional[list[str]] = None
    room_id: Optional[str] = None
    room_ids: Optional[list[str]] = None
    camera_id: Optional[str] = None
    case_id: Optional[str] = None

    min_time: Optional[datetime] = datetime_field()
    max_time: Optional[datetime] = datetime_field()
    min_start_time: Optional[datetime] = datetime_field()
    max_start_time: Optional[datetime] = datetime_field()
    min_created_time: Optional[datetime] = datetime_field()
    max_created_time: Optional[datetime] = datetime_field()

    source_type: Optional[str] = None
    source_types: Optional[List[str]] = None
    model_version: Optional[str] = None
    labels: Optional[List[str]] = None
    min_confidence: Optional[float] = None
    notes: Optional[str] = None

    event_type: Optional[str] = None
    exclude_event_names: Optional[List[str]] = None
    event_names: Optional[List[str]] = None
    include_deleted: Optional[bool] = None
    include_dashboard_events_only: Optional[bool] = None


@dataclass
class EventHistoryQueryDto(EventQueryDto):
    event_ids: Optional[List[str]] = None


@dataclass
class EventQueryResultDto(DataClassJsonMixin):
    events: Optional[List[EventDto]] = None


@dataclass
class BatchEventCreateRequestDto(DataClassJsonMixin):
    events: Optional[List[EventDto]] = None


@dataclass
class BatchEventCreateResultDto(DataClassJsonMixin):
    events_created: Optional[int] = None
    duration_seconds: Optional[float] = None


@dataclass
class MediaAssetInfoDto(DataClassJsonMixin):
    asset_id: Optional[str] = None
    asset_type: Optional[str] = None
    org_id: Optional[str] = field(serialized_name="organization_id")
    site_id: Optional[str] = None
    camera_id: Optional[str] = None
    room_id: Optional[str] = None
    camera_name: Optional[str] = None
    asset_uri: Optional[str] = None
    start_time: Optional[datetime] = datetime_field()
    end_time: Optional[datetime] = datetime_field()
    content_type: Optional[str] = None
    asset_resolution: Optional[str] = None
    metadata: Optional[dict[str, Any]] = None


@dataclass
class MediaAssetQueryResultDto(DataClassJsonMixin):
    assets: Optional[List[MediaAssetInfoDto]] = None


@dataclass
class MediaAssetUrlDto(DataClassJsonMixin):
    url: str
    expiration: datetime = datetime_field()


@dataclass
class MediaAssetDeletionUris(DataClassJsonMixin):
    asset_uris: Optional[List[str]] = None


@dataclass
class HighlightAssetDto(DataClassJsonMixin):
    highlight_id: Optional[UUID] = uuid_field()
    asset_id: Optional[str] = None


class EventChangelogAction(Enum):
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"


@dataclass(frozen=True)
class EventChangeDto(DataClassJsonMixin):
    event_id: Optional[str] = None
    event_name: Optional[str] = None
    action: Optional[EventChangelogAction] = None
    organization_id: Optional[str] = None
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    camera_id: Optional[str] = None
    start_time: Optional[datetime] = datetime_field()
    source_type: Optional[str] = None


@dataclass
class ContactInformationQueryDto(DataClassJsonMixin):
    ids: Optional[List[str]] = None
    staff_ids: Optional[List[str]] = None
    case_ids: Optional[List[str]] = None
    contact_information_values: Optional[List[str]] = None
    types: Optional[List[str]] = None
    event_type_ids: Optional[List[str]] = None
    initialized: Optional[bool] = None


@dataclass
class StaffEventNotificationContactInformationUpsertAndArchiveDto(DataClassJsonMixin):
    staff_id: UUID
    contact_information_value: str
    type: int
    event_type_id: str
    archived_time: Optional[datetime] = datetime_field()


@dataclass(frozen=True)
class ContactInformationDTO:
    contact_information_value: str
    type: str


@dataclass
class RoomClosureQueryDto(DataClassJsonMixin):
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    room_ids: Optional[List[str]] = None
    min_end_time: Optional[datetime] = datetime_field()
    max_start_time: Optional[datetime] = datetime_field()

    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class SiteClosureQueryDto(DataClassJsonMixin):
    site_id: Optional[str] = None
    closure_date: Optional[date] = datetime_field()
    start_date: Optional[date] = datetime_field()
    end_date: Optional[date] = datetime_field()

    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)


@dataclass
class BlockTimeQueryDto(DataClassJsonMixin):
    site_id: Optional[str] = None
    room_id: Optional[str] = None
    room_ids: Optional[List[str]] = None
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)
    min_end_time: datetime = datetime_field()
    max_start_time: datetime = datetime_field()


@dataclass
class BoardConfigQueryDto(DataClassJsonMixin):
    room_ids: Optional[List[str]] = None
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)
    site_id: Optional[str] = None
    site_ids: Optional[List[str]] = None
    org_ids: Optional[List[str]] = None


@dataclass
class BlockQueryDto(DataClassJsonMixin):
    query_id: uuid.UUID = dataclasses.field(default_factory=uuid.uuid4)

    min_end_time: Optional[datetime] = datetime_field()
    max_start_time: Optional[datetime] = datetime_field()
    days_of_week: Optional[List[int]] = None

    ids: Optional[List[str]] = None
    names: Optional[List[str]] = None
    org_ids: Optional[list[str]] = None
    site_ids: Optional[list[str]] = None
    room_ids: Optional[list[str]] = None
    surgeon_ids: Optional[list[str]] = None

    include_archived: Optional[bool] = None


@dataclass(frozen=True)
class MatchCaseDTO:
    case_id: str
    phase_id: Optional[UUID]
    phase_etag: Optional[UUID]
    case_match_type: Optional[int]
    explanation_for_change: str


@dataclass
class CaseDurationAvailableSlotsRequestDto(DataClassJsonMixin):
    startDate: str
    endDate: str
    procedureName: str
    surgeonName: str
    siteId: str
    surgeonId: str
    siteTimezone: str


@dataclass
class AvailableSlot(DataClassJsonMixin):
    room_id: str
    block_time_ids: list[str]
    start_time: datetime = datetime_field()
    end_time: datetime = datetime_field()
    max_available_duration: timedelta = timedelta_field()


@dataclass
class AvailableSlots(DataClassJsonMixin):
    standard: list[AvailableSlot]
    complex: list[AvailableSlot]


@dataclass
class BlockUtilizationDto(DataClassJsonMixin):
    block_id: uuid.UUID
    utilized_scheduled_seconds: int
    total_scheduled_case_seconds: int
    total_case_seconds: int
    total_actual_case_seconds: int
    utilized_seconds: int
    available_seconds: int
    total_block_seconds: int
    cases_for_block_day: list["CaseToBlock"]
    date: date


@dataclass
class CaseToBlock:
    case_id: str
    site_id: str
    block_id: str
    block_date: date
    score: int
    scheduled_case_seconds: int
    status: str
    actual_case_seconds: Optional[int] = None
    utilized_scheduled_case_seconds: Optional[int] = None
    utilized_case_seconds: Optional[int] = None
    override_turnover_seconds: Optional[int] = None
    override_utilized_case_seconds: Optional[int] = None
