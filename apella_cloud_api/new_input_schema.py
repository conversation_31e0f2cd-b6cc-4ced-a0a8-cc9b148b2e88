from dataclasses import dataclass

from typing import Union
from uuid import UUID  # noqa
from datetime import datetime, date, timedelta
from .new_schema_generator.schema_generator_base_classes import GQLInputObject, Empty

from .new_client_schema import (  # noqa
    GQLAdministrativeSex,
    GQLAdministrativeSexType,
    GQLAgeRange,
    GQLAnesthesia,
    GQLAnesthesiaConnection,
    GQLAnesthesiaEdge,
    GQLAnesthesiasUpsert,
    GQLAnnnotationTaskTypeCreate,
    GQLAnnnotationTaskTypeUpdate,
    GQLAnnotationTask,
    GQLAnnotationTaskBulkGenerate,
    GQLAnnotationTaskBulkUpdate,
    GQLAnnotationTaskConnection,
    GQLAnnotationTaskEdge,
    GQLAnnotationTaskExit,
    GQLAnnotationTaskNextAnnotate,
    GQLAnnotationTaskNextReview,
    GQLAnnotationTaskSchedule,
    GQLAnnotationTaskScheduleCreate,
    GQLAnnotationTaskScheduleUpdate,
    GQLAnnotation<PERSON><PERSON>Type,
    GQLAnnotationTaskUpdate,
    GQLAnnotationTasksCount,
    GQLApellaCase,
    GQLApellaCaseConnection,
    GQLApellaCaseEdge,
    GQLApellaCaseStatus,
    GQLAvailableTimeSlot,
    GQLBlock,
    GQLBlockArchive,
    GQLBlockConnection,
    GQLBlockCreate,
    GQLBlockEdge,
    GQLBlockReleaseFileTransform,
    GQLBlockReleaseProcessDateRange,
    GQLBlockReleaseReprocess,
    GQLBlockTime,
    GQLBlockTimeAvailableInterval,
    GQLBlockTimeAvailableIntervalConnection,
    GQLBlockTimeAvailableIntervalEdge,
    GQLBlockTimeBulkCreate,
    GQLBlockTimeBulkDeleteDuplicate,
    GQLBlockTimeConnection,
    GQLBlockTimeEdge,
    GQLBlockTimeRelease,
    GQLBlockUnarchive,
    GQLBlockUpdate,
    GQLBlockUtilization,
    GQLBoardConfig,
    GQLBoardConfigConnection,
    GQLBoardConfigCreate,
    GQLBoardConfigDelete,
    GQLBoardConfigEdge,
    GQLBoardConfigUpdate,
    GQLBoardViewType,
    GQLCamera,
    GQLCameraCaptureLatestImage,
    GQLCameraConnection,
    GQLCameraCreate,
    GQLCameraEdge,
    GQLCameraLatestImage,
    GQLCameraLatestImageConnection,
    GQLCameraLatestImageEdge,
    GQLCameraUpdate,
    GQLCamerasCreate,
    GQLCancelledReason,
    GQLCaseClassificationType,
    GQLCaseDurationPredictions,
    GQLCaseDurationProcedureOption,
    GQLCaseDurationProcedureOptionConnection,
    GQLCaseDurationProcedureOptionEdge,
    GQLCaseDurationSurgeonOption,
    GQLCaseDurationSurgeonOptionConnection,
    GQLCaseDurationSurgeonOptionEdge,
    GQLCaseDurationSurgeonProcedureMapping,
    GQLCaseDurationSurgeonProcedureMappingConnection,
    GQLCaseDurationSurgeonProcedureMappingEdge,
    GQLCaseDurationTurnoverPrediction,
    GQLCaseEhrMessage,
    GQLCaseEhrMessageConnection,
    GQLCaseEhrMessageEdge,
    GQLCaseFlag,
    GQLCaseFlagConnection,
    GQLCaseFlagEdge,
    GQLCaseFlagUpsert,
    GQLCaseForecast,
    GQLCaseForecastConnection,
    GQLCaseForecastEdge,
    GQLCaseForecastStatus,
    GQLCaseForecastUpsert,
    GQLCaseLabel,
    GQLCaseLabelCategory,
    GQLCaseLabelField,
    GQLCaseLabelFieldOption,
    GQLCaseLabelFieldType,
    GQLCaseLabelUpsert,
    GQLCaseMatchingStatus,
    GQLCaseNotePlan,
    GQLCaseNotePlanUpsert,
    GQLCaseProcedure,
    GQLCaseProceduresUpsert,
    GQLCaseProceduresUpsertAndArchive,
    GQLCaseSource,
    GQLCaseStaff,
    GQLCaseStaffPlan,
    GQLCaseStaffPlanConnection,
    GQLCaseStaffPlanEdge,
    GQLCaseStaffPlanUpsert,
    GQLCaseStaffRole,
    GQLCaseStaffUpsert,
    GQLCaseStaffUpsertAndArchive,
    GQLCaseStatusName,
    GQLCaseToBlock,
    GQLCaseToBlockConnection,
    GQLCaseToBlockEdge,
    GQLCaseToBlockOverride,
    GQLCaseToBlockOverridesUpsert,
    GQLCaseType,
    GQLCheckNotificationsErrors,
    GQLCleanScoreEnum,
    GQLCluster,
    GQLClusterConnection,
    GQLClusterCreate,
    GQLClusterEdge,
    GQLClusterUpdate,
    GQLContactInformation,
    GQLContactInformationConnection,
    GQLContactInformationEdge,
    GQLContactInformationType,
    GQLCustomPhaseConfig,
    GQLCustomPhaseConfigDelete,
    GQLCustomPhaseConfigUpsert,
    GQLDayOfWeek,
    GQLDefaultSiteClosuresCreate,
    GQLDirection,
    GQLEmailAvailableTimes,
    GQLEmailAvailableTimesHtml,
    GQLEvent,
    GQLEventConnection,
    GQLEventCreate,
    GQLEventDashboardVisibility,
    GQLEventDashboardVisibilityDelete,
    GQLEventDashboardVisibilityUpsert,
    GQLEventDelete,
    GQLEventEdge,
    GQLEventLabelOption,
    GQLEventLabelOptionCreate,
    GQLEventLabelOptionDelete,
    GQLEventLabelOptionUpdate,
    GQLEventMatchingStatus,
    GQLEventNotification,
    GQLEventType,
    GQLEventTypeConnection,
    GQLEventTypeCreate,
    GQLEventTypeEdge,
    GQLEventTypeUpdate,
    GQLEventUpdate,
    GQLEventUpsert,
    GQLFeedbackStatus,
    GQLFieldCount,
    GQLFirstCaseConfigSource,
    GQLHighlight,
    GQLHighlightArchive,
    GQLHighlightConnection,
    GQLHighlightCreate,
    GQLHighlightDelete,
    GQLHighlightEdge,
    GQLHighlightFeedback,
    GQLHighlightFeedbackConnection,
    GQLHighlightFeedbackCreate,
    GQLHighlightFeedbackEdge,
    GQLHighlightFeedbackUpdate,
    GQLHighlightUpdate,
    GQLMatchCases,
    GQLMatchingStatusReason,
    GQLMeasurementPeriod,
    GQLMeasurementPeriodConnection,
    GQLMeasurementPeriodDelete,
    GQLMeasurementPeriodEdge,
    GQLMeasurementPeriodUpsert,
    GQLMetricBucketFloat,
    GQLMetricFloat,
    GQLMissingNotification,
    GQLMultiMetricBucketFloat,
    GQLMutation,
    GQLNotifyStaffForEvents,
    GQLObjectMetrics,
    GQLObservation,
    GQLObservationConnection,
    GQLObservationCreate,
    GQLObservationDelete,
    GQLObservationEdge,
    GQLObservationType,
    GQLObservationTypeName,
    GQLObservationsUpsert,
    GQLOrganization,
    GQLOrganizationConnection,
    GQLOrganizationCreate,
    GQLOrganizationEdge,
    GQLOrganizationUpdate,
    GQLPageCursor,
    GQLPageCursors,
    GQLPageInfo,
    GQLPatient,
    GQLPatientBoundingBox,
    GQLPatientClass,
    GQLPersonalInfo,
    GQLPhase,
    GQLPhaseConnection,
    GQLPhaseCreate,
    GQLPhaseDelete,
    GQLPhaseEdge,
    GQLPhaseRelationshipCreate,
    GQLPhaseRelationshipDelete,
    GQLPhaseStatus,
    GQLPhaseType,
    GQLPhaseTypeConnection,
    GQLPhaseTypeEdge,
    GQLPhaseTypeRecord,
    GQLPhaseUpdate,
    GQLPhaseUpsert,
    GQLPredictionMetadata,
    GQLPrimeTimeConfigSource,
    GQLProcedure,
    GQLProcedureConnection,
    GQLProcedureEdge,
    GQLProceduresUpsert,
    GQLProcessCaseDerivedProperties,
    GQLQuery,
    GQLRoom,
    GQLRoomClosure,
    GQLRoomClosureConnection,
    GQLRoomClosureCreate,
    GQLRoomClosureDelete,
    GQLRoomClosureEdge,
    GQLRoomConnection,
    GQLRoomCreate,
    GQLRoomEdge,
    GQLRoomFirstCaseConfig,
    GQLRoomFirstCaseConfigDelete,
    GQLRoomFirstCaseConfigUpsert,
    GQLRoomPrimeTimeConfig,
    GQLRoomPrimeTimeConfigDelete,
    GQLRoomPrimeTimeConfigUpsert,
    GQLRoomSetTags,
    GQLRoomStatus,
    GQLRoomStatusName,
    GQLRoomTag,
    GQLRoomTagConnection,
    GQLRoomTagCreate,
    GQLRoomTagEdge,
    GQLRoomTagRename,
    GQLRoomTagUpdate,
    GQLRoomUpdate,
    GQLRoomUpdateConfiguration,
    GQLRoomsCreate,
    GQLScheduledCase,
    GQLScheduledCaseConnection,
    GQLScheduledCaseEdge,
    GQLServiceLine,
    GQLSite,
    GQLSiteCapacityConstraint,
    GQLSiteClosure,
    GQLSiteClosureConnection,
    GQLSiteClosureCreate,
    GQLSiteClosureDelete,
    GQLSiteClosureEdge,
    GQLSiteConnection,
    GQLSiteCreate,
    GQLSiteEdge,
    GQLSiteFirstCaseConfig,
    GQLSiteFirstCaseConfigSource,
    GQLSiteFirstCaseConfigUpsert,
    GQLSiteLaunchesUpsert,
    GQLSitePrimeTimeConfig,
    GQLSitePrimeTimeConfigSource,
    GQLSitePrimeTimeConfigUpsert,
    GQLSiteUpdate,
    GQLStaff,
    GQLStaffCode,
    GQLStaffCodesUpsert,
    GQLStaffConnection,
    GQLStaffEdge,
    GQLStaffEventNotificationContactInformation,
    GQLStaffEventNotificationContactInformationConnection,
    GQLStaffEventNotificationContactInformationEdge,
    GQLStaffRole,
    GQLStaffUpsert,
    GQLStaffingNeedsRatio,
    GQLStaffingNeedsRatioCreate,
    GQLSubscribersUpsert,
    GQLTaskStatus,
    GQLTerminalCleanScore,
    GQLTerminalCleanScoreUpsert,
    GQLTimeRange,
    GQLTurnover,
    GQLTurnoverGoals,
    GQLTurnoverGoalsUpdate,
    GQLTurnoverLabel,
    GQLTurnoverLabelNoteUpsert,
    GQLTurnoverStatusGraphene,
    GQLTurnoverStatusName,
    GQLTurnoverType,
    GQLUpsertForecastsForCases,
    GQLUser,
    GQLUserConnection,
    GQLUserEdge,
    GQLUserFilterView,
    GQLUserFilterViewCreate,
    GQLUserFilterViewDelete,
    GQLUserFilterViewUpdate,
    GQLUserUiPermissions,
)


@dataclass
class GQLOrderBy(GQLInputObject):
    sort: "str"
    direction: "Union[GQLDirection, Empty]" = Empty()


@dataclass
class GQLEventNotficationQueryInput(GQLInputObject):
    case_ids: "Union[list[str], Empty, None]" = Empty()
    event_ids: "Union[list[str], Empty, None]" = Empty()
    event_type_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLCaseStaffPlanInput(GQLInputObject):
    include_archived: "Union[bool, Empty]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLTurnoverQueryInput(GQLInputObject):
    max_start_time: "datetime"
    min_end_time: "datetime"
    case_ids: "Union[list[str], Empty, None]" = Empty()
    meets_inclusion_criteria: "Union[bool, Empty]" = Empty()
    phase_ids: "Union[list[str], Empty, None]" = Empty()
    turnover_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLApellaCaseBaseQueryInput(GQLInputObject):
    max_start_time: "datetime"
    min_end_time: "datetime"
    case_ids: "Union[list[str], Empty, None]" = Empty()
    case_matching_statuses: "Union[list[GQLCaseMatchingStatus], Empty, None]" = Empty()
    case_types: "Union[list[GQLCaseType], Empty, None]" = Empty()
    phase_ids: "Union[list[str], Empty, None]" = Empty()
    scheduled_case_status: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLRoomEventSearchInput(GQLInputObject):
    camera_id: "Union[str, Empty, None]" = Empty()
    event_names: "Union[list[str], Empty, None]" = Empty()
    event_type: "Union[str, Empty, None]" = Empty()
    exclude_event_names: "Union[list[str], Empty, None]" = Empty()
    include_dashboard_events_only: "Union[bool, Empty]" = Empty()
    include_deleted: "Union[bool, Empty]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    min_confidence: "Union[float, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    model_version: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()
    source_type: "Union[str, Empty, None]" = Empty()
    source_types: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLBlockTimeQueryInput(GQLInputObject):
    max_start_time: "datetime"
    min_end_time: "datetime"
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLUsersSearchInput(GQLInputObject):
    role: "str"


@dataclass
class GQLRoomClosureQueryInput(GQLInputObject):
    max_start_time: "Union[datetime, Empty]" = Empty()
    min_end_time: "Union[datetime, Empty]" = Empty()
    room_id: "Union[str, Empty, None]" = Empty()
    room_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLSiteClosureQueryInput(GQLInputObject):
    closure_date: "Union[date, Empty]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLEventSearchInput(GQLInputObject):
    camera_id: "Union[str, Empty, None]" = Empty()
    event_names: "Union[list[str], Empty, None]" = Empty()
    event_type: "Union[str, Empty, None]" = Empty()
    exclude_event_names: "Union[list[str], Empty, None]" = Empty()
    include_dashboard_events_only: "Union[bool, Empty]" = Empty()
    include_deleted: "Union[bool, Empty]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    min_confidence: "Union[float, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    model_version: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    room_id: "Union[str, Empty, None]" = Empty()
    room_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()
    site_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    source_type: "Union[str, Empty, None]" = Empty()
    source_types: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLEventHistorySearchInput(GQLInputObject):
    camera_id: "Union[str, Empty, None]" = Empty()
    event_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    event_names: "Union[list[str], Empty, None]" = Empty()
    event_type: "Union[str, Empty, None]" = Empty()
    exclude_event_names: "Union[list[str], Empty, None]" = Empty()
    include_dashboard_events_only: "Union[bool, Empty]" = Empty()
    include_deleted: "Union[bool, Empty]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    min_confidence: "Union[float, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    model_version: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    room_id: "Union[str, Empty, None]" = Empty()
    room_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()
    site_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    source_type: "Union[str, Empty, None]" = Empty()
    source_types: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLHighlightInput(GQLInputObject):
    max_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    status: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLHighlightSearchInput(GQLInputObject):
    max_time: "datetime"
    min_time: "datetime"
    assigned_user_ids: "Union[list[str], Empty, None]" = Empty()
    categories: "Union[list[str], Empty, None]" = Empty()
    feedback_status: "Union[GQLFeedbackStatus, Empty]" = Empty()
    organization_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLHighlightFeedbackSearchInput(GQLInputObject):
    max_time: "datetime"
    min_time: "datetime"


@dataclass
class GQLAnnotationTaskQueryInput(GQLInputObject):
    annotator_user_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    end_time: "Union[datetime, Empty]" = Empty()
    max_updated_time: "Union[datetime, Empty]" = Empty()
    min_updated_time: "Union[datetime, Empty]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    reviewer_user_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    room_id: "Union[str, Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()
    start_time: "Union[datetime, Empty]" = Empty()
    statuses: "Union[list[GQLTaskStatus], Empty, None]" = Empty()
    type_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLPhaseQueryInput(GQLInputObject):
    case_ids: "Union[list[str], Empty, None]" = Empty()
    ensure_phase_start_time_has_not_elapsed: "Union[bool, Empty]" = Empty()
    max_created_time: "Union[datetime, Empty]" = Empty()
    max_duration: "Union[timedelta, Empty]" = Empty()
    max_end_time: "Union[datetime, Empty]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    max_updated_time: "Union[datetime, Empty]" = Empty()
    min_created_time: "Union[datetime, Empty]" = Empty()
    min_end_time: "Union[datetime, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    min_updated_time: "Union[datetime, Empty]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    show_human_ground_truth_data: "Union[bool, Empty]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    source_type: "Union[str, Empty, None]" = Empty()
    statuses: "Union[list[GQLPhaseStatus], Empty, None]" = Empty()
    type: "Union[GQLPhaseType, Empty]" = Empty()


@dataclass
class GQLApellaCaseQueryInput(GQLInputObject):
    max_start_time: "datetime"
    min_end_time: "datetime"
    case_ids: "Union[list[str], Empty, None]" = Empty()
    case_matching_statuses: "Union[list[GQLCaseMatchingStatus], Empty, None]" = Empty()
    case_types: "Union[list[GQLCaseType], Empty, None]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    phase_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    scheduled_case_status: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLOccupancyBucketInput(GQLInputObject):
    max_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    room_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLScheduledCaseQueryInput(GQLInputObject):
    case_classification_types_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    case_flags: "Union[list[str], Empty, None]" = Empty()
    case_ids: "Union[list[str], Empty, None]" = Empty()
    is_add_ons: "Union[list[Union[bool, Empty]], Empty, None]" = Empty()
    max_created_time: "Union[datetime, Empty]" = Empty()
    max_end_time: "Union[datetime, Empty]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    max_updated_time: "Union[datetime, Empty]" = Empty()
    min_created_time: "Union[datetime, Empty]" = Empty()
    min_end_time: "Union[datetime, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    min_updated_time: "Union[datetime, Empty]" = Empty()
    procedure_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()
    status: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseHistoryQueryInput(GQLInputObject):
    case_classification_types_ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    case_flags: "Union[list[str], Empty, None]" = Empty()
    case_ids: "Union[list[str], Empty, None]" = Empty()
    is_add_ons: "Union[list[Union[bool, Empty]], Empty, None]" = Empty()
    max_created_time: "Union[datetime, Empty]" = Empty()
    max_end_time: "Union[datetime, Empty]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    max_time: "Union[datetime, Empty]" = Empty()
    max_updated_time: "Union[datetime, Empty]" = Empty()
    min_created_time: "Union[datetime, Empty]" = Empty()
    min_end_time: "Union[datetime, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    min_time: "Union[datetime, Empty]" = Empty()
    min_updated_time: "Union[datetime, Empty]" = Empty()
    procedure_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()
    status: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseEhrMessageQueryInput(GQLInputObject):
    min_event_time: "datetime"
    org_id: "str"
    case_id: "Union[str, Empty, None]" = Empty()
    event_types: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    latest_per_case_id: "Union[bool, Empty]" = Empty()
    max_event_time: "Union[datetime, Empty]" = Empty()
    no_case_id: "Union[bool, Empty]" = Empty()
    raw_message_text_search: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLLiveCameraImagesInput(GQLInputObject):
    camera_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLServiceLineQueryInput(GQLInputObject):
    ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    org_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLProcedureQueryInput(GQLInputObject):
    case_id: "Union[str, Empty, None]" = Empty()
    hierarchy: "Union[int, Empty]" = Empty()
    names: "Union[list[str], Empty, None]" = Empty()
    org_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLAnesthesiaQueryInput(GQLInputObject):
    names: "Union[list[str], Empty, None]" = Empty()
    org_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLMeasurementPeriodQueryInput(GQLInputObject):
    measurement_period_end: "date"
    measurement_period_start: "date"
    site_id: "str"
    annotation_task_type_id: "Union[str, Empty, None]" = Empty()
    names: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLStaffQueryInput(GQLInputObject):
    case_id: "Union[str, Empty, None]" = Empty()
    ids: "Union[list[str], Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    only_primary_surgeons: "Union[bool, Empty]" = Empty()
    org_id: "Union[str, Empty, None]" = Empty()
    staff_code: "Union[GQLStaffCodeQueryInput, Empty]" = Empty()
    staff_roles: "Union[list[GQLCaseStaffRole], Empty, None]" = Empty()


@dataclass
class GQLStaffCodeQueryInput(GQLInputObject):
    code: "str"
    coding_system: "str"


@dataclass
class GQLObservationSearchInput(GQLInputObject):
    case_ids: "Union[list[str], Empty, None]" = Empty()
    max_observation_time: "Union[datetime, Empty]" = Empty()
    max_recorded_time: "Union[datetime, Empty]" = Empty()
    min_observation_time: "Union[datetime, Empty]" = Empty()
    min_recorded_time: "Union[datetime, Empty]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    type_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLObservationTypeNamesInput(GQLInputObject):
    org_id: "Union[str, Empty, None]" = Empty()
    type_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLObservationTypeNamesInputForCustomPhases(GQLInputObject):
    org_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLContactInformationSearchInput(GQLInputObject):
    case_ids: "Union[list[str], Empty, None]" = Empty()
    contact_information_values: "Union[list[str], Empty, None]" = Empty()
    event_type_ids: "Union[list[str], Empty, None]" = Empty()
    ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()
    types: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLStaffEventNotificationContactInformationSearchInput(GQLInputObject):
    event_type_ids: "Union[list[str], Empty, None]" = Empty()
    org_ids: "Union[list[str], Empty, None]" = Empty()
    staff_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLBlockQueryInput(GQLInputObject):
    days_of_week: "Union[list[int], Empty, None]" = Empty()
    ids: "Union[list[str], Empty, None]" = Empty()
    include_archived: "Union[bool, Empty]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    min_end_time: "Union[datetime, Empty]" = Empty()
    names: "Union[list[str], Empty, None]" = Empty()
    org_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    surgeon_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLBlockTimesBulkQueryInput(GQLInputObject):
    ids: "list[str]"


@dataclass
class GQLBlockTimeAvailableIntervalInput(GQLInputObject):
    max_start_time: "datetime"
    min_end_time: "datetime"
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLBoardConfigQueryInput(GQLInputObject):
    organization_ids: "Union[list[str], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLCaseDurationPredictionQueryInput(GQLInputObject):
    additional_procedures: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    procedure: "Union[str, Empty, None]" = Empty()
    surgeon_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseDurationSurgeonsQueryInput(GQLInputObject):
    surgeon_id: "Union[str, Empty, None]" = Empty()
    surgeon_term: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseDurationProceduresQueryInput(GQLInputObject):
    procedure_term: "Union[str, Empty, None]" = Empty()
    surgeon_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLAvailableTimeSlotQueryInput(GQLInputObject):
    min_available_duration: "timedelta"
    site_ids: "list[Union[str, Empty, None]]"
    end_date: "Union[date, Empty]" = Empty()
    start_date: "Union[date, Empty]" = Empty()
    surgeon_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLUserFilterViewQueryInput(GQLInputObject):
    ids: "Union[list[Union[str, Empty, None]], Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseForecastQueryInput(GQLInputObject):
    case_ids: "Union[list[str], Empty, None]" = Empty()
    forecast_statuses: "Union[list[GQLCaseForecastStatus], Empty, None]" = Empty()
    forecast_variants: "Union[list[str], Empty, None]" = Empty()
    max_start_time: "Union[datetime, Empty]" = Empty()
    min_start_time: "Union[datetime, Empty]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLGQLTurnoverLabelQueryInput(GQLInputObject):
    type: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseToBlockInput(GQLInputObject):
    max_date: "datetime"
    min_date: "datetime"
    site_id: "str"


@dataclass
class GQLCaseToBlockOverrideInput(GQLInputObject):
    block_ids: "Union[list[str], Empty, None]" = Empty()
    case_ids: "Union[list[str], Empty, None]" = Empty()
    max_date: "Union[datetime, Empty]" = Empty()
    min_date: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLBlockUtilizationInput(GQLInputObject):
    max_date: "datetime"
    min_date: "datetime"
    site_id: "str"


@dataclass
class GQLEventCreateInput(GQLInputObject):
    id: "str"
    name: "str"
    organization_id: "str"
    process_time: "datetime"
    room_id: "str"
    site_id: "str"
    source: "str"
    source_type: "str"
    start_time: "datetime"
    camera_id: "Union[str, Empty, None]" = Empty()
    confidence: "Union[float, Empty]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    model_version: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLEventUpdateInput(GQLInputObject):
    id: "str"
    source: "str"
    source_type: "str"
    camera_id: "Union[str, Empty, None]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()
    publish_changelog: "Union[bool, Empty]" = Empty()
    start_time: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLEventDeleteInput(GQLInputObject):
    id: "str"
    source: "str"
    source_type: "str"
    publish_changelog: "Union[bool, Empty]" = Empty()


@dataclass
class GQLEventUpsertInput(GQLInputObject):
    event_name: "str"
    organization_id: "str"
    process_timestamp: "datetime"
    room_id: "str"
    site_id: "str"
    source: "str"
    source_type: "str"
    start_time: "datetime"
    camera_id: "Union[str, Empty, None]" = Empty()
    confidence: "Union[float, Empty]" = Empty()
    etag: "Union[str, Empty, None]" = Empty()
    event_matching_status: "Union[GQLEventMatchingStatus, Empty]" = Empty()
    event_type: "Union[str, Empty, None]" = Empty()
    event_type_id: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    labels: "Union[list[str], Empty, None]" = Empty()
    model_version: "Union[str, Empty, None]" = Empty()
    notes: "Union[str, Empty, None]" = Empty()
    publish_changelog: "Union[bool, Empty]" = Empty()


@dataclass
class GQLEventTypeCreateInput(GQLInputObject):
    color: "str"
    hidden: "bool"
    name: "str"
    type: "str"
    description: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLEventTypeUpdateInput(GQLInputObject):
    id: "str"
    color: "Union[str, Empty, None]" = Empty()
    description: "Union[str, Empty, None]" = Empty()
    hidden: "Union[bool, Empty]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    type: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLEventLabelOptionCreateInput(GQLInputObject):
    name: "str"
    id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLEventLabelOptionUpdateInput(GQLInputObject):
    id: "str"
    name: "str"


@dataclass
class GQLEventLabelOptionDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLHighlightCreateInput(GQLInputObject):
    assigned_user_ids: "list[str]"
    description: "str"
    end_time: "datetime"
    id: "str"
    organization_id: "str"
    room_id: "str"
    site_id: "str"
    start_time: "datetime"
    camera_id: "Union[str, Empty, None]" = Empty()
    category: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLHighlightUpdateInput(GQLInputObject):
    assigned_user_ids: "list[str]"
    description: "str"
    end_time: "datetime"
    id: "str"
    organization_id: "str"
    room_id: "str"
    site_id: "str"
    start_time: "datetime"
    camera_id: "Union[str, Empty, None]" = Empty()
    category: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLHighlightDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLHighlightArchiveInput(GQLInputObject):
    id: "str"


@dataclass
class GQLHighlightFeedbackCreateInput(GQLInputObject):
    highlight_id: "UUID"
    id: "str"
    comment: "Union[str, Empty, None]" = Empty()
    rating: "Union[int, Empty]" = Empty()


@dataclass
class GQLHighlightFeedbackUpdateInput(GQLInputObject):
    highlight_id: "UUID"
    id: "str"
    comment: "Union[str, Empty, None]" = Empty()
    rating: "Union[int, Empty]" = Empty()


@dataclass
class GQLAnnotationTaskBulkGenerateInput(GQLInputObject):
    end_time: "Union[datetime, Empty]" = Empty()
    start_time: "Union[datetime, Empty]" = Empty()
    task_type_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLAnnotationTaskBulkUpdateInput(GQLInputObject):
    update_input: "GQLAnnotationTaskBulkUpdateUpdateInput"
    query_input: "Union[GQLAnnotationTaskQueryInput, Empty]" = Empty()
    task_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLAnnotationTaskBulkUpdateUpdateInput(GQLInputObject):
    annotator_user_id: "Union[str, Empty, None]" = Empty()
    cancelled_reason: "Union[GQLCancelledReason, Empty]" = Empty()
    reviewer_user_id: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLTaskStatus, Empty]" = Empty()


@dataclass
class GQLAnnotationTaskUpdateInput(GQLInputObject):
    id: "str"
    annotator_user_id: "Union[str, Empty, None]" = Empty()
    cancelled_reason: "Union[GQLCancelledReason, Empty]" = Empty()
    reviewer_user_id: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLTaskStatus, Empty]" = Empty()


@dataclass
class GQLAnnotationTaskExitInput(GQLInputObject):
    task_id: "str"


@dataclass
class GQLAnnnotationTaskTypeCreateInput(GQLInputObject):
    description: "str"
    name: "str"
    allow_skipping_review: "Union[bool, Empty]" = Empty()
    annotator_ids: "Union[list[str], Empty, None]" = Empty()
    context_event_types: "Union[list[str], Empty, None]" = Empty()
    detect_idle: "Union[bool, Empty]" = Empty()
    event_types: "Union[list[str], Empty, None]" = Empty()
    optimize_tasks: "Union[bool, Empty]" = Empty()
    priority: "Union[int, Empty]" = Empty()
    provisional_annotator_ids: "Union[list[str], Empty, None]" = Empty()
    reviewer_ids: "Union[list[str], Empty, None]" = Empty()
    schedules: "Union[list[GQLAnnotationTaskScheduleInput], Empty, None]" = Empty()


@dataclass
class GQLAnnotationTaskScheduleInput(GQLInputObject):
    interval: "int"
    start_time: "datetime"
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLAnnnotationTaskTypeUpdateInput(GQLInputObject):
    id: "str"
    allow_skipping_review: "Union[bool, Empty]" = Empty()
    annotator_ids: "Union[list[str], Empty, None]" = Empty()
    archived: "Union[bool, Empty]" = Empty()
    context_event_types: "Union[list[str], Empty, None]" = Empty()
    description: "Union[str, Empty, None]" = Empty()
    detect_idle: "Union[bool, Empty]" = Empty()
    event_types: "Union[list[str], Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    optimize_tasks: "Union[bool, Empty]" = Empty()
    priority: "Union[int, Empty]" = Empty()
    provisional_annotator_ids: "Union[list[str], Empty, None]" = Empty()
    reviewer_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLAnnotationTaskScheduleCreateInput(GQLInputObject):
    annotation_task_type_id: "str"
    interval: "int"
    start_time: "datetime"
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLAnnotationTaskScheduleUpdateInput(GQLInputObject):
    id: "str"
    interval: "Union[int, Empty]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    start_time: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLAnnotationTaskNextAnnotateInput(GQLInputObject):
    current_task_id: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLTaskStatus, Empty]" = Empty()


@dataclass
class GQLAnnotationTaskNextReviewInput(GQLInputObject):
    current_task_id: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLTaskStatus, Empty]" = Empty()


@dataclass
class GQLCameraCaptureLatestImageInput(GQLInputObject):
    camera_id: "str"
    capture_time: "datetime"
    image_uri: "str"


@dataclass
class GQLCaseProceduresUpsertInput(GQLInputObject):
    case_id: "str"
    procedure_id: "str"
    anesthesia_id: "Union[UUID, Empty]" = Empty()
    hierarchy: "Union[int, Empty]" = Empty()


@dataclass
class GQLProcessCaseDerivedPropertiesInput(GQLInputObject):
    site_id: "str"
    date: "Union[date, Empty]" = Empty()


@dataclass
class GQLCaseNotePlanUpsertInput(GQLInputObject):
    case_id: "str"
    note: "str"
    org_id: "str"
    site_id: "str"
    id: "Union[UUID, Empty]" = Empty()


@dataclass
class GQLCaseProceduresUpsertAndArchiveInput(GQLInputObject):
    case_id: "str"
    procedure_id: "UUID"
    anesthesia_id: "Union[UUID, Empty]" = Empty()
    hierarchy: "Union[int, Empty]" = Empty()


@dataclass
class GQLCaseStaffUpsertInput(GQLInputObject):
    case_id: "str"
    staff_id: "UUID"
    role: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseStaffUpsertAndArchiveInput(GQLInputObject):
    case_id: "str"
    staff_id: "UUID"
    role: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseStaffPlanUpsertInput(GQLInputObject):
    case_id: "str"
    org_id: "str"
    site_id: "str"
    staff_id: "str"
    archived_time: "Union[datetime, Empty]" = Empty()
    id: "Union[UUID, Empty]" = Empty()
    role: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseFlagUpsertInput(GQLInputObject):
    case_id: "str"
    flag_type: "str"
    org_id: "str"
    site_id: "str"
    archived: "Union[bool, Empty]" = Empty()
    id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLMatchCaseInput(GQLInputObject):
    case_id: "str"
    explanation_for_change: "str"
    case_match_type: "Union[GQLCaseMatchingStatus, Empty]" = Empty()
    phase_etag: "Union[UUID, Empty]" = Empty()
    phase_id: "Union[UUID, Empty]" = Empty()


@dataclass
class GQLProcedureUpsertInput(GQLInputObject):
    name: "str"
    org_id: "str"


@dataclass
class GQLAnesthesiaUpsertInput(GQLInputObject):
    name: "str"
    org_id: "str"


@dataclass
class GQLMeasurementPeriodDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLMeasurementPeriodUpsertInput(GQLInputObject):
    annotation_task_type_id: "str"
    measurement_period_end: "date"
    measurement_period_start: "date"
    name: "str"
    site_id: "str"
    days_of_week: "Union[list[GQLDayOfWeek], Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    iso_days_of_week: "Union[list[int], Empty, None]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLStaffUpsertInput(GQLInputObject):
    external_staff_id: "str"
    first_name: "str"
    last_name: "str"
    org_id: "str"


@dataclass
class GQLStaffCodeUpsertInput(GQLInputObject):
    code: "str"
    coding_system: "str"
    org_id: "str"
    staff_id: "str"


@dataclass
class GQLPhaseDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLPhaseUpdateInput(GQLInputObject):
    id: "str"
    case_id: "Union[str, Empty, None]" = Empty()
    end_event_id: "Union[str, Empty, None]" = Empty()
    etag: "Union[str, Empty, None]" = Empty()
    invalidation_reason: "Union[str, Empty, None]" = Empty()
    source_type: "Union[str, Empty, None]" = Empty()
    start_event_id: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLPhaseStatus, Empty]" = Empty()
    type_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLPhaseCreateInput(GQLInputObject):
    id: "str"
    org_id: "str"
    room_id: "str"
    site_id: "str"
    source_type: "str"
    start_event_id: "str"
    type_id: "str"
    case_id: "Union[str, Empty, None]" = Empty()
    end_event_id: "Union[str, Empty, None]" = Empty()
    invalidation_reason: "Union[str, Empty, None]" = Empty()
    status: "Union[GQLPhaseStatus, Empty]" = Empty()


@dataclass
class GQLPhaseUpsertInput(GQLInputObject):
    org_id: "str"
    room_id: "str"
    site_id: "str"
    source_type: "str"
    start_event_id: "str"
    status: "GQLPhaseStatus"
    type_id: "str"
    case_id: "Union[str, Empty, None]" = Empty()
    end_event_id: "Union[str, Empty, None]" = Empty()
    etag: "Union[str, Empty, None]" = Empty()
    event_matching_status: "Union[GQLEventMatchingStatus, Empty]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    invalidation_reason: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLPhaseRelationshipDeleteInput(GQLInputObject):
    child_phase_id: "str"
    parent_phase_id: "str"


@dataclass
class GQLPhaseRelationshipCreateInput(GQLInputObject):
    child_phase_id: "str"
    org_id: "str"
    parent_phase_id: "str"


@dataclass
class GQLObservationCreateInput(GQLInputObject):
    case_id: "str"
    id: "str"
    observation_time: "datetime"
    organization_id: "str"
    type_id: "str"
    recorded_time: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLObservationDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLObservationUpsertInput(GQLInputObject):
    case_id: "str"
    observation_time: "datetime"
    organization_id: "str"
    type_id: "str"
    recorded_time: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLSubscriberUpsertInput(GQLInputObject):
    contact_information_value: "str"
    first_name: "str"
    last_name: "str"
    subscriptions: "list[GQLSubscriberEventNotificationUpsertInput]"
    type: "GQLContactInformationType"
    contact_information_id: "Union[UUID, Empty]" = Empty()
    is_apella_employee: "Union[bool, Empty]" = Empty()


@dataclass
class GQLSubscriberEventNotificationUpsertInput(GQLInputObject):
    event_type_id: "str"
    staff_id: "UUID"
    archive: "Union[bool, Empty]" = Empty()


@dataclass
class GQLStaffingNeedsRatioCreateInput(GQLInputObject):
    id: "str"
    organization_id: "str"
    site_id: "str"
    staff_role_id: "str"
    ratio: "Union[float, Empty]" = Empty()
    set_by_user_id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLStaffEventsNotificationsInput(GQLInputObject):
    site_id: "str"
    confidence_threshold: "Union[float, Empty]" = Empty()
    site_name: "Union[str, Empty, None]" = Empty()
    time_threshold: "Union[timedelta, Empty]" = Empty()
    time_to_check: "Union[datetime, Empty]" = Empty()
    time_window_to_search: "Union[timedelta, Empty]" = Empty()


@dataclass
class GQLEmailAvailableTimesInput(GQLInputObject):
    end_date: "date"
    recipients: "list[str]"
    slots: "list[GQLAvailableTimeSlotInput]"
    start_date: "date"
    sender_email: "Union[str, Empty, None]" = Empty()
    sender_name: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLAvailableTimeSlotInput(GQLInputObject):
    end_time: "datetime"
    max_available_duration: "timedelta"
    room_id: "str"
    start_time: "datetime"


@dataclass
class GQLEmailAvailableTimesHtmlInput(GQLInputObject):
    end_date: "date"
    slots: "list[GQLAvailableTimeSlotInput]"
    start_date: "date"


@dataclass
class GQLBlockCreateInput(GQLInputObject):
    name: "str"
    org_id: "str"
    block_times: "Union[list[GQLBlockTimeInput], Empty, None]" = Empty()
    color: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    site_ids: "Union[list[str], Empty, None]" = Empty()
    surgeon_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLBlockTimeInput(GQLInputObject):
    end_time: "datetime"
    room_id: "str"
    start_time: "datetime"
    block_id: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    releases: "Union[list[GQLBlockTimeReleaseInput], Empty, None]" = Empty()


@dataclass
class GQLBlockTimeReleaseInput(GQLInputObject):
    end_time: "datetime"
    reason: "str"
    source: "str"
    start_time: "datetime"
    block_time_id: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    released_time: "Union[datetime, Empty]" = Empty()
    to_block: "Union[str, Empty, None]" = Empty()
    to_block_type: "Union[str, Empty, None]" = Empty()
    unreleased_source: "Union[str, Empty, None]" = Empty()
    unreleased_time: "Union[datetime, Empty]" = Empty()


@dataclass
class GQLBlockUpdateInput(GQLInputObject):
    block_times: "list[GQLBlockTimeInput]"
    color: "str"
    id: "str"
    name: "str"
    site_ids: "Union[list[str], Empty, None]" = Empty()
    surgeon_ids: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLBlockTimeBulkCreateInput(GQLInputObject):
    block_times: "list[GQLBlockTimeInput]"


@dataclass
class GQLBlockReleaseReprocessInput(GQLInputObject):
    end_date: "date"
    org_id: "str"
    start_date: "date"


@dataclass
class GQLBlockReleaseProcessDateRangeInput(GQLInputObject):
    end_date: "date"
    room_ids: "list[Union[str, Empty, None]]"
    start_date: "date"


@dataclass
class GQLBlockTimeBulkDeleteDuplicateInput(GQLInputObject):
    org_id: "str"


@dataclass
class GQLBoardConfigCreateInput(GQLInputObject):
    blur_video: "bool"
    board_view_type: "GQLBoardViewType"
    enable_video: "bool"
    name: "str"
    org_id: "str"
    page_duration: "int"
    page_size: "int"
    site_id: "str"
    id: "Union[UUID, Empty]" = Empty()
    room_ids: "Union[list[str], Empty, None]" = Empty()
    show_closed_rooms: "Union[bool, Empty]" = Empty()
    zoom_percent: "Union[int, Empty]" = Empty()


@dataclass
class GQLBoardConfigUpdateInput(GQLInputObject):
    blur_video: "bool"
    board_config_id: "str"
    board_view_type: "GQLBoardViewType"
    enable_video: "bool"
    name: "str"
    page_duration: "int"
    page_size: "int"
    room_ids: "Union[list[str], Empty, None]" = Empty()
    show_closed_rooms: "Union[bool, Empty]" = Empty()
    zoom_percent: "Union[int, Empty]" = Empty()


@dataclass
class GQLBoardConfigDeleteInput(GQLInputObject):
    board_config_id: "str"


@dataclass
class GQLOrganizationCreateInput(GQLInputObject):
    auth0_org_id: "str"
    id: "str"
    name: "str"


@dataclass
class GQLOrganizationUpdateInput(GQLInputObject):
    id: "str"
    auth0_org_id: "Union[str, Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLSiteCreateInput(GQLInputObject):
    id: "str"
    name: "str"
    organization_id: "str"
    timezone: "str"


@dataclass
class GQLSiteUpdateInput(GQLInputObject):
    id: "str"
    name: "Union[str, Empty, None]" = Empty()
    organization_id: "Union[str, Empty, None]" = Empty()
    timezone: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLSitePrimeTimeConfigUpsertInput(GQLInputObject):
    site_id: "str"
    friday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    monday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    saturday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    sunday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    thursday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    tuesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    wednesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()


@dataclass
class GQLDayOfWeekInput(GQLInputObject):
    end_time: "datetime"
    start_time: "datetime"
    constraints: "Union[list[Union[GQLCapacityConstraintInput, Empty]], Empty, None]" = Empty()


@dataclass
class GQLCapacityConstraintInput(GQLInputObject):
    count: "int"
    start_time: "datetime"


@dataclass
class GQLSiteClosureCreateInput(GQLInputObject):
    closure_date: "date"
    reason: "str"
    site_id: "str"


@dataclass
class GQLSiteClosureDeleteInput(GQLInputObject):
    site_closure_id: "str"


@dataclass
class GQLDefaultSiteClosuresCreateInput(GQLInputObject):
    end_date: "date"
    start_date: "date"


@dataclass
class GQLSiteFirstCaseConfigUpsertInput(GQLInputObject):
    site_id: "str"
    friday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    monday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    saturday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    sunday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    thursday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    tuesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    wednesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()


@dataclass
class GQLSiteLaunchUpsertInput(GQLInputObject):
    site_id: "str"
    actual_launch_date: "Union[date, Empty]" = Empty()
    anticipated_launch_date: "Union[date, Empty]" = Empty()


@dataclass
class GQLRoomCreateInput(GQLInputObject):
    id: "str"
    name: "str"
    organization_id: "str"
    site_id: "str"
    default_camera_id: "Union[str, Empty, None]" = Empty()
    is_forecasting_enabled: "Union[bool, Empty]" = Empty()
    labels: "Union[dict[str, str], Empty]" = Empty()


@dataclass
class GQLRoomUpdateInput(GQLInputObject):
    id: "str"
    default_camera_id: "Union[str, Empty, None]" = Empty()
    is_forecasting_enabled: "Union[bool, Empty]" = Empty()
    labels: "Union[dict[str, str], Empty]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    privacy_enabled: "Union[bool, Empty]" = Empty()
    sort_key: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLRoomUpdateConfigurationInput(GQLInputObject):
    id: "str"
    privacy_enabled: "Union[bool, Empty]" = Empty()


@dataclass
class GQLRoomTagCreateInput(GQLInputObject):
    name: "str"
    org_id: "str"
    color: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLRoomSetTagsInput(GQLInputObject):
    room_id: "str"
    tag_ids: "list[str]"


@dataclass
class GQLRoomTagRenameInput(GQLInputObject):
    name: "str"
    tag_id: "str"


@dataclass
class GQLRoomTagUpdateInput(GQLInputObject):
    tag_id: "str"
    color: "Union[str, Empty, None]" = Empty()
    name: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLRoomClosureCreateInput(GQLInputObject):
    end_time: "datetime"
    room_id: "str"
    start_time: "datetime"


@dataclass
class GQLRoomClosureDeleteInput(GQLInputObject):
    room_closure_id: "str"


@dataclass
class GQLRoomPrimeTimeConfigUpsertInput(GQLInputObject):
    room_id: "str"
    friday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    monday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    saturday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    sunday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    thursday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    tuesday: "Union[GQLTimeRangeInput, Empty]" = Empty()
    wednesday: "Union[GQLTimeRangeInput, Empty]" = Empty()


@dataclass
class GQLTimeRangeInput(GQLInputObject):
    end_time: "datetime"
    start_time: "datetime"


@dataclass
class GQLRoomPrimeTimeConfigDeleteInput(GQLInputObject):
    room_id: "str"


@dataclass
class GQLRoomFirstCaseConfigUpsertInput(GQLInputObject):
    room_id: "str"
    friday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    monday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    saturday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    sunday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    thursday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    tuesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()
    wednesday: "Union[GQLDayOfWeekInput, Empty]" = Empty()


@dataclass
class GQLRoomFirstCaseConfigDeleteInput(GQLInputObject):
    room_id: "str"


@dataclass
class GQLCameraCreateInput(GQLInputObject):
    family: "str"
    id: "str"
    name: "str"
    organization_id: "str"
    room_id: "str"
    site_id: "str"
    labels: "Union[dict[str, str], Empty]" = Empty()
    rtsp_url: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCameraUpdateInput(GQLInputObject):
    id: "str"
    family: "Union[str, Empty, None]" = Empty()
    labels: "Union[dict[str, str], Empty]" = Empty()
    name: "Union[str, Empty, None]" = Empty()
    rtsp_url: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLUserFilterViewCreateInput(GQLInputObject):
    id: "str"
    name: "str"
    url: "str"


@dataclass
class GQLUserFilterViewUpdateInput(GQLInputObject):
    id: "str"
    name: "Union[str, Empty, None]" = Empty()
    url: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLClusterCreateInput(GQLInputObject):
    name: "str"
    sites: "list[str]"
    enable_audio: "Union[bool, Empty]" = Empty()
    id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLClusterUpdateInput(GQLInputObject):
    id: "str"
    name: "str"
    sites: "list[str]"
    enable_audio: "Union[bool, Empty]" = Empty()


@dataclass
class GQLTurnoverGoalsUpdateInput(GQLInputObject):
    org_id: "str"
    site_id: "str"
    goal_minutes: "Union[int, Empty]" = Empty()
    max_minutes: "Union[int, Empty]" = Empty()


@dataclass
class GQLCaseForecastUpsertInput(GQLInputObject):
    case_id: "str"
    forecast_end_time: "datetime"
    forecast_start_time: "datetime"
    forecast_status: "GQLCaseForecastStatus"
    forecast_variant: "str"
    organization_id: "str"
    room_id: "str"
    site_id: "str"
    bayesian_duration_minutes: "Union[float, Empty]" = Empty()
    bayesian_end_time: "Union[datetime, Empty]" = Empty()
    case_start_source: "Union[str, Empty, None]" = Empty()
    id: "Union[str, Empty, None]" = Empty()
    is_auto_follow: "Union[bool, Empty]" = Empty()
    is_overtime: "Union[bool, Empty]" = Empty()
    pythia_duration_minutes: "Union[float, Empty]" = Empty()
    pythia_end_time: "Union[datetime, Empty]" = Empty()
    pythia_prediction_tag: "Union[str, Empty, None]" = Empty()
    static_duration_end_time: "Union[datetime, Empty]" = Empty()
    static_duration_minutes: "Union[float, Empty]" = Empty()
    static_start_offset_minutes: "Union[float, Empty]" = Empty()
    transformer_end_time: "Union[datetime, Empty]" = Empty()
    turnover_duration_minutes: "Union[float, Empty]" = Empty()


@dataclass
class GQLCaseForecastForCaseInput(GQLInputObject):
    case_id: "str"
    forecast_end_time: "datetime"
    forecast_start_time: "datetime"
    forecast_status: "GQLCaseForecastStatus"
    forecast_variant: "str"
    organization_id: "str"
    room_id: "str"
    site_id: "str"
    bayesian_duration_minutes: "Union[float, Empty]" = Empty()
    bayesian_end_time: "Union[datetime, Empty]" = Empty()
    case_start_source: "Union[str, Empty, None]" = Empty()
    is_auto_follow: "Union[bool, Empty]" = Empty()
    is_overtime: "Union[bool, Empty]" = Empty()
    pythia_duration_minutes: "Union[float, Empty]" = Empty()
    pythia_end_time: "Union[datetime, Empty]" = Empty()
    pythia_prediction_tag: "Union[str, Empty, None]" = Empty()
    static_duration_end_time: "Union[datetime, Empty]" = Empty()
    static_duration_minutes: "Union[float, Empty]" = Empty()
    static_start_offset_minutes: "Union[float, Empty]" = Empty()
    transformer_end_time: "Union[datetime, Empty]" = Empty()
    turnover_duration_minutes: "Union[float, Empty]" = Empty()


@dataclass
class GQLEventDashboardVisibilityUpsertInput(GQLInputObject):
    event_type_id: "str"
    org_id_filter: "Union[list[str], Empty, None]" = Empty()


@dataclass
class GQLEventDashboardVisibilityDeleteInput(GQLInputObject):
    event_type_id: "str"


@dataclass
class GQLTerminalCleanScoreUpsertInput(GQLInputObject):
    date: "date"
    room_id: "str"
    comments: "Union[str, Empty, None]" = Empty()
    score: "Union[GQLCleanScoreEnum, Empty]" = Empty()


@dataclass
class GQLTurnoverLabelNoteUpsertInput(GQLInputObject):
    id: "str"
    label_ids: "Union[list[str], Empty, None]" = Empty()
    note: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCaseToBlockOverrideUpsertInput(GQLInputObject):
    block_date: "date"
    block_id: "str"
    case_id: "str"
    user_id: "str"
    note: "Union[str, Empty, None]" = Empty()
    utilized_procedure_minutes: "Union[int, Empty]" = Empty()
    utilized_turnover_minutes: "Union[int, Empty]" = Empty()


@dataclass
class GQLCustomPhaseConfigUpsertInput(GQLInputObject):
    description: "str"
    end_event_type: "str"
    name: "str"
    start_event_type: "str"
    id: "Union[str, Empty, None]" = Empty()


@dataclass
class GQLCustomPhaseConfigDeleteInput(GQLInputObject):
    id: "str"


@dataclass
class GQLCaseLabelUpsertInput(GQLInputObject):
    case_id: "str"
    id: "str"
    option_id: "str"
    archived_time: "Union[datetime, Empty]" = Empty()
