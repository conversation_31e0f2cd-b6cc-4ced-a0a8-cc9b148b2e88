from collections import defaultdict
from datetime import date
from typing import DefaultD<PERSON>
from uuid import <PERSON><PERSON><PERSON>
from zoneinfo import ZoneInfo

from apella_cloud_api.api_server_schema import BlockTime
from api_server.services.block.block_models import BlockTimeInterval
from api_server.services.block_utilization.block_utilization_store import BlockTimeOverrideModel
from apella_cloud_api.dtos import CaseToBlock


def get_cases_for_block_by_day(
    cases_to_blocks: list[CaseToBlock],
) -> DefaultDict[date, list[CaseToBlock]]:
    cases_for_block_by_day = defaultdict(list)
    for case_to_block in cases_to_blocks:
        cases_for_block_by_day[case_to_block.block_date].append(case_to_block)
    return cases_for_block_by_day


def get_block_times_by_day(
    block_times: list[BlockTime], tz: ZoneInfo
) -> DefaultDict[date, list[BlockTime]]:
    block_times_by_day = defaultdict(list)
    for block_time in block_times:
        block_time_end_date = block_time.end_time.astimezone(tz).date()
        block_times_by_day[block_time_end_date].append(block_time)
    return block_times_by_day


def get_block_time_intervals_by_day(
    block_time_intervals: list[BlockTimeInterval], tz: ZoneInfo
) -> DefaultDict[date, list[BlockTimeInterval]]:
    block_time_intervals_by_day = defaultdict(list)
    for block_time_interval in block_time_intervals:
        block_time_end_date = block_time_interval.end_time.astimezone(tz).date()
        block_time_intervals_by_day[block_time_end_date].append(block_time_interval)
    return block_time_intervals_by_day


def get_block_time_overrides_by_block_time_id(
    block_time_overrides: list[BlockTimeOverrideModel],
) -> DefaultDict[UUID, BlockTimeOverrideModel]:
    block_time_overrides_by_block_time_id = defaultdict(BlockTimeOverrideModel)
    for block_time_override in block_time_overrides:
        block_time_overrides_by_block_time_id[block_time_override.block_time_id] = (
            block_time_override
        )
    return block_time_overrides_by_block_time_id
