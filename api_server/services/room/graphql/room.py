# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs
from datetime import datetime
from typing import Optional, Sequence, Type
from zoneinfo import ZoneInfo

import api_server.services.closures.graphql.room_closure as room_closure_schema
from api_server.services.closures.graphql.room_closure_loader import FrozenRoomClosureQueryDto
from api_server.services.first_case_config.dtos import RoomFirstCaseConfigDto
from api_server.services.first_case_config.graphql.room_first_case_config import RoomFirstCaseConfig
from api_server.services.prime_time.dtos import RoomPrimeTime
import api_server.services.prime_time.graphql.room_prime_time_config as room_prime_time_config_schema
from api_server.services.closures.closure_store import RoomClosure
import graphene
from graphql import GraphQLError

import api_server.services.apella_case.graphql.apella_case as apella_case_schema
import api_server.services.camera.graphql.camera as camera_schema
import api_server.services.events.graphql.event as event_schema
import api_server.services.organization.graphql.organization as organization_schema
import api_server.services.room.graphql.room_tag as room_tag_schema
import api_server.services.site.graphql.site as site_schema
from api_server.graphql import scalar
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination import PaginationConnection
from api_server.graphql.pagination.fields import pagination_connection_factory
from api_server.graphql.sorting.arguments import SortedPaginationConnectionField
from api_server.services.apella_case.apella_case_store import ApellaCase
from api_server.services.apella_case.graphql.apella_case_loader import (
    FrozenRoomApellaCasesQueryDto,
)
from api_server.services.block.block_store import BlockTimeModel
from api_server.services.block.graphql.blocks import (
    BlockTimeConnection,
    BlockTimeQueryInput,
)
from api_server.services.camera.camera_store import CameraModel
from api_server.services.events.event_store import EventModel
from api_server.services.events.graphql.event_loaders import FrozenRoomEventsQueryDto
from api_server.services.events.graphql.event_search_input import RoomEventSearchInput
from api_server.services.media.graphql.playlist_loader import (
    PlaylistAvailabilityLoaderKey,
)
from api_server.services.organization.organization_db import Organization
from api_server.services.room.room_service import RoomStatusNameGraphene
from api_server.services.room.room_store import RoomModel
from api_server.services.site.site_store import Site
from api_server.services.turnover.graphql.turnover import Turnover, TurnoverQueryInput
from api_server.tracing import ApellaTracer


class RoomStatus(graphene.ObjectType):
    name = RoomStatusNameGraphene(required=True)
    since = graphene.DateTime(required=True)
    calculated_at = graphene.DateTime(required=True)
    in_progress_apella_case = graphene.Field(lambda: apella_case_schema.ApellaCase, required=False)
    in_progress_turnover = graphene.Field(Turnover, required=False)
    next_case = graphene.Field(lambda: apella_case_schema.ApellaCase, required=False)

    @staticmethod
    async def resolve_calculated_at(room_status, info: GrapheneInfo, **kwargs):
        """Map the computed_at field from the dataclass to the calculated_at GraphQL field."""
        return room_status.computed_at


class Room(graphene.ObjectType):
    id = graphene.ID(required=True)

    name = graphene.String(required=True)

    organization_id = graphene.String(required=True)
    organization = graphene.Field(lambda: organization_schema.Organization, required=True)

    site_id = graphene.String(required=True)
    site = graphene.Field(lambda: site_schema.Site, required=True)

    cameras: list[CameraModel] = SortedPaginationConnectionField(
        lambda: camera_schema.CameraConnection, required=True
    )
    default_camera = graphene.Field(lambda: camera_schema.Camera)

    status = graphene.Field(
        lambda: RoomStatus,
        min_end_time=graphene.DateTime(default_value=None),
        max_start_time=graphene.DateTime(default_value=None),
        required=True,
    )
    sort_key = graphene.String()

    has_playlist_available = graphene.Boolean(
        min_time=graphene.DateTime(required=True),
        max_time=graphene.DateTime(required=True),
        required=True,
    )

    events = graphene.List(
        lambda: graphene.NonNull(event_schema.Event),
        min_time=graphene.DateTime(required=True),
        max_time=graphene.DateTime(required=True),
        source_type=graphene.String(),
        model_version=graphene.String(),
        event_type=graphene.String(),
        event_names=graphene.List(graphene.NonNull(graphene.String), default_value=None),
        required=True,
        deprecation_reason="Deprecated in favor in `room_events` to support sorting and pagination",
    )

    turnovers = graphene.List(
        lambda: graphene.NonNull(Turnover),
        query=graphene.Argument(TurnoverQueryInput, required=True),
        required=True,
    )

    apella_cases = SortedPaginationConnectionField(
        lambda: apella_case_schema.ApellaCaseConnection,
        query=graphene.Argument(apella_case_schema.ApellaCaseBaseQueryInput, required=True),
        required=True,
    )

    room_events = SortedPaginationConnectionField(
        lambda: event_schema.EventConnection,
        query=graphene.Argument(RoomEventSearchInput, required=True),
        required=True,
    )

    block_times = SortedPaginationConnectionField(
        lambda: BlockTimeConnection,
        query=graphene.Argument(BlockTimeQueryInput, required=True),
        required=True,
    )

    privacy_enabled = graphene.Boolean(required=True)

    is_forecasting_enabled = graphene.Boolean(required=True)

    tags = graphene.List(
        lambda: graphene.NonNull(room_tag_schema.RoomTag),
        required=True,
    )

    closures = SortedPaginationConnectionField(
        lambda: room_closure_schema.RoomClosureConnection,
        required=True,
    )

    prime_time_config = graphene.Field(
        lambda: room_prime_time_config_schema.RoomPrimeTimeConfig, required=True
    )

    first_case_config = graphene.Field(lambda: RoomFirstCaseConfig, required=True)

    labels = scalar.JSON(required=False)

    @staticmethod
    async def resolve_labels(room: RoomModel, info: GrapheneInfo, **kwargs):
        return room.labels

    @staticmethod
    async def resolve_organization_id(room: RoomModel, info: GrapheneInfo, **kwargs):
        return room.org_id

    @staticmethod
    async def resolve_organization(room: RoomModel, info: GrapheneInfo, **kwargs) -> Organization:
        organization: Optional[Organization] = await info.context.org_loader.load(room.org_id)
        if organization is None:
            raise GraphQLError(
                message=f"Unable to find organization for room with org id: {room.org_id}"
            )
        return organization

    @staticmethod
    async def resolve_site(room: RoomModel, info: GrapheneInfo, **kwargs) -> Site:
        site: Optional[Site] = await info.context.site_loader.load(room.site_id)
        if site is None:
            raise GraphQLError(message=f"Unable to find site for room with site id: {room.site_id}")
        return site

    @staticmethod
    async def resolve_cameras(room: RoomModel, info: GrapheneInfo, **kwargs):
        cameras = await info.context.room_cameras_loader.load(room.id)
        return cameras

    @staticmethod
    async def resolve_default_camera(
        room: RoomModel, info: GrapheneInfo, **kwargs
    ) -> Optional[CameraModel]:
        return await info.context.default_camera_loader.load(room.id)

    @staticmethod
    async def resolve_status(
        room: RoomModel,
        info: GrapheneInfo,
        min_end_time: Optional[datetime] = None,
        max_start_time: Optional[datetime] = None,
        **kwargs,
    ):
        site = await info.context.site_loader.load(room.site_id)
        assert isinstance(site, Site)

        # Use provided date parameters if available, otherwise fall back to current date
        if min_end_time is not None and max_start_time is not None:
            query_min_end_time = min_end_time
            query_max_start_time = max_start_time
            # Use the provided date for status calculation
            status_calculation_time = min_end_time.astimezone(tz=ZoneInfo(site.timezone))
        else:
            # Fallback to current date behavior for backward compatibility
            now = datetime.now().astimezone(tz=ZoneInfo(site.timezone))
            query_min_end_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query_max_start_time = now.replace(hour=23, minute=59, second=59, microsecond=999000)
            status_calculation_time = now

        case_query = FrozenRoomApellaCasesQueryDto(
            org_id=room.org_id,
            # Querying for site_ids=(room.site_id,) makes the loader execute separate queries per site.
            site_ids=None,
            room_id=room.id,
            min_end_time=query_min_end_time,
            # Match the front-end package, luxon's behavior of `endOf('day')` which sets microsecond
            # to '999000' so that the loader combines the queries for `resolve_status` and
            # `resolve_apella_cases` or `resolve_turnovers`.
            max_start_time=query_max_start_time,
            case_ids=None,
            case_types=None,
            phase_ids=None,
            staff_ids=None,
            case_matching_statuses=None,
            scheduled_case_status=None,
        )

        with ApellaTracer.tracer.start_as_current_span("room_apella_case_loader.load"):
            cases = await info.context.room_apella_case_loader.load(case_query)
            turnover_goals = await info.context.turnover_goals_loader.load(room.site_id)
            return await info.context.room_service.get_room_status(
                list(cases),
                status_calculation_time,
                turnover_goals.max_minutes if turnover_goals else None,
            )

    @staticmethod
    async def resolve_has_playlist_available(
        room: RoomModel, info: GrapheneInfo, min_time, max_time
    ) -> bool:
        return await info.context.playlist_loader.load(
            PlaylistAvailabilityLoaderKey(room_id=room.id, min_time=min_time, max_time=max_time)
        )

    @staticmethod
    async def resolve_privacy_enabled(room: RoomModel, info: GrapheneInfo, **kwargs):
        return room.privacy_enabled_at is not None

    @staticmethod
    async def resolve_events(
        room: RoomModel,
        info: GrapheneInfo,
        min_time: datetime,
        max_time: datetime,
        source_type: Optional[str] = None,
        model_version: Optional[str] = None,
        event_type: Optional[str] = None,
        event_names: Optional[list[str]] = None,
    ) -> Sequence[EventModel]:
        return await info.context.room_events_loader.load(
            FrozenRoomEventsQueryDto(
                organization_id=room.org_id,
                site_id=room.site_id,
                room_id=room.id,
                min_time=min_time,
                max_time=max_time,
                source_type=source_type,
                model_version=model_version,
                event_type=event_type,
                event_names=tuple(event_names) if event_names is not None else None,
            )
        )

    @staticmethod
    async def resolve_closures(
        room: RoomModel,
        info: GrapheneInfo,
    ) -> Sequence[RoomClosure]:
        return await info.context.room_closure_loader.load(
            FrozenRoomClosureQueryDto(room_id=room.id)
        )

    @staticmethod
    async def resolve_prime_time_config(
        room: RoomModel,
        info: GrapheneInfo,
    ) -> RoomPrimeTime:
        return await info.context.room_prime_time_config_loader.load(room.id)

    @staticmethod
    async def resolve_first_case_config(
        room: RoomModel,
        info: GrapheneInfo,
    ) -> RoomFirstCaseConfigDto:
        return await info.context.room_first_case_config_loader.load(room.id)

    @staticmethod
    async def resolve_apella_cases(
        room: RoomModel,
        info: GrapheneInfo,
        query: apella_case_schema.ApellaCaseBaseQueryInput,
        **kwargs,
    ) -> Sequence[ApellaCase]:
        case_query = FrozenRoomApellaCasesQueryDto(
            org_id=room.org_id,
            # Querying for site_ids=(room.site_id,) makes the loader execute separate queries per site.
            site_ids=None,
            room_id=room.id,
            case_ids=tuple(query.case_ids) if query.case_ids else None,
            phase_ids=tuple(query.phase_ids) if query.phase_ids else None,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
            case_types=tuple(query.case_types) if query.case_types else None,
            case_matching_statuses=tuple(query.case_matching_statuses)
            if query.case_matching_statuses
            else None,
            scheduled_case_status=query.scheduled_case_status,
            staff_ids=tuple(query.staff_ids) if query.staff_ids else None,
        )
        return await info.context.room_apella_case_loader.load(case_query)

    @staticmethod
    async def resolve_room_events(
        room: RoomModel,
        info: GrapheneInfo,
        query: RoomEventSearchInput,
        **kwargs,
    ) -> Sequence[EventModel]:
        return await info.context.room_events_loader.load(query.to_frozen_dto(room=room))

    @staticmethod
    async def resolve_block_times(
        room: RoomModel,
        info: GrapheneInfo,
        query: BlockTimeQueryInput,
        **kwargs,
    ) -> Sequence[BlockTimeModel]:
        return await info.context.room_block_time_loader.load(query.to_frozen_dto(room=room))

    @staticmethod
    async def resolve_turnovers(
        room: RoomModel,
        info: GrapheneInfo,
        query: TurnoverQueryInput,
        **kwargs,
    ):
        case_query = FrozenRoomApellaCasesQueryDto(
            org_id=room.org_id,
            # Querying for site_ids=(room.site_id,) makes the loader execute separate queries per site.
            site_ids=None,
            room_id=room.id,
            case_ids=tuple(query.case_ids) if query.case_ids else None,
            phase_ids=tuple(query.phase_ids) if query.phase_ids else None,
            min_end_time=query.min_end_time,
            max_start_time=query.max_start_time,
            case_types=None,
            case_matching_statuses=None,
            scheduled_case_status=None,
            staff_ids=None,
        )
        cases = await info.context.room_apella_case_loader.load(case_query)

        turnovers = await info.context.turnover_service.calculate_turnovers_for_room(cases)

        if query.turnover_id is not None:
            turnovers = [t for t in turnovers if t.id == query.turnover_id]
        if query.meets_inclusion_criteria:
            turnovers = [t for t in turnovers if t.meets_inclusion_criteria]

        return turnovers

    @staticmethod
    async def resolve_tags(
        room: RoomModel,
        info: GrapheneInfo,
        **kwargs,
    ):
        return await info.context.room_tag_loader.load(room.id)


RoomConnection: Type[PaginationConnection] = pagination_connection_factory(Room)
