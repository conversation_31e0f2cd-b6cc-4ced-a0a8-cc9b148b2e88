from api_server.services.block.graphql.block_releases_reprocess import (
    BlockReleaseFileTransform,
    BlockReleaseProcessDateRange,
    BlockReleaseReprocess,
)
from api_server.services.case_forecasts.graphql.case_forecast_upsert import CaseForecastUpsert
from api_server.services.case_forecasts.graphql.upsert_forecasts_for_cases import (
    UpsertForecastsForCases,
)
from api_server.services.case_labels.graphql.case_label_upsert import Case<PERSON>abelUpsert
from api_server.services.case_to_block.graphql.case_to_block_override_upsert import (
    CaseToBlockOverridesUpsert,
)
from api_server.services.closures.graphql.default_site_closures_create import (
    DefaultSiteClosuresCreate,
)
from api_server.services.closures.graphql.room_closure_create import RoomClosureCreate
from api_server.services.closures.graphql.room_closure_delete import RoomClosureDelete
from api_server.services.closures.graphql.site_closure_create import SiteClosureCreate
from api_server.services.closures.graphql.site_closure_delete import SiteClosureDelete

from api_server.services.events.graphql.event_dashboard_visibility_delete import (
    EventDashboardVisibilityDelete,
)
from api_server.services.events.graphql.event_dashboard_visibility_upsert import (
    EventDashboardVisibilityUpsert,
)
from api_server.services.first_case_config.graphql.room_first_case_config_delete import (
    RoomFirstCaseConfigDelete,
)
from api_server.services.first_case_config.graphql.room_first_case_config_upsert import (
    RoomFirstCaseConfigUpsert,
)
from api_server.services.first_case_config.graphql.site_first_case_config_upsert import (
    SiteFirstCaseConfigUpsert,
)
from api_server.services.prime_time.graphql.room_prime_time_config_delete import (
    RoomPrimeTimeConfigDelete,
)
from api_server.services.prime_time.graphql.room_prime_time_config_upsert import (
    RoomPrimeTimeConfigUpsert,
)
from api_server.services.room.graphql.room_set_tags import RoomSetTags
from api_server.services.room.graphql.room_tag_create import RoomTagCreate
from api_server.services.room.graphql.room_tag_update import RoomTagUpdate
from api_server.services.prime_time.graphql.site_prime_time_config_upsert import (
    SitePrimeTimeConfigUpsert,
)
from api_server.services.site.graphql.upsert_site_launches import SiteLaunchesUpsert
from api_server.services.terminal_cleans.graphql.terminal_clean_score_upsert import (
    TerminalCleanScoreUpsert,
)
import graphene

from api_server.services.anesthesia.graphql.anesthesia_mutations import (
    AnesthesiasUpsert,
)
from api_server.services.annotation_tasks.graphql.annotation_task_bulk_generate import (
    AnnotationTaskBulkGenerate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_bulk_update import (
    AnnotationTaskBulkUpdate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_exit import (
    AnnotationTaskExit,
)
from api_server.services.annotation_tasks.graphql.annotation_task_next_annotate import (
    AnnotationTaskNextAnnotate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_next_review import (
    AnnotationTaskNextReview,
)
from api_server.services.annotation_tasks.graphql.annotation_task_schedule_create import (
    AnnotationTaskScheduleCreate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_schedule_update import (
    AnnotationTaskScheduleUpdate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_type_create import (
    AnnnotationTaskTypeCreate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_type_update import (
    AnnnotationTaskTypeUpdate,
)
from api_server.services.annotation_tasks.graphql.annotation_task_update import (
    AnnotationTaskUpdate,
)
from api_server.services.block.graphql.block_mutations import (
    BlockArchive,
    BlockCreate,
    BlockTimeBulkCreate,
    BlockTimeBulkDeleteDuplicate,
    BlockUnarchive,
    BlockUpdate,
)
from api_server.services.boards.graphql.board_config_create import BoardConfigCreate
from api_server.services.boards.graphql.board_config_delete import BoardConfigDelete
from api_server.services.boards.graphql.board_config_update import BoardConfigUpdate
from api_server.services.camera.graphql.camera_create import CameraCreate
from api_server.services.camera.graphql.camera_update import CameraUpdate
from api_server.services.camera.graphql.cameras_create import CamerasCreate
from api_server.services.case.graphql.case_flag_upsert import CaseFlagUpsert
from api_server.services.case.graphql.case_procedures_upsert import CaseProceduresUpsert
from api_server.services.case.graphql.case_procedures_upsert_and_archive import (
    CaseProceduresUpsertAndArchive,
)
from api_server.services.case.graphql.case_staff_upsert import CaseStaffUpsert
from api_server.services.case.graphql.case_staff_upsert_and_archive import (
    CaseStaffUpsertAndArchive,
)
from api_server.services.cluster.graphql.cluster_mutation import (
    ClusterCreate,
    ClusterUpdate,
)
from api_server.services.case.graphql.match_cases import MatchCases
from api_server.services.case_derived_properties.graphql.process_case_derived_properties import (
    ProcessCaseDerivedProperties,
)
from api_server.services.contact_information.graphql.check_notifications_errors import (
    CheckNotificationsErrors,
)
from api_server.services.contact_information.graphql.notify_staff_for_events import (
    NotifyStaffForEvents,
)
from api_server.services.contact_information.graphql.upsert_subscribers import (
    SubscribersUpsert,
)
from api_server.services.custom_phase_config.graphql.custom_phase_config_upsert import (
    CustomPhaseConfigUpsert,
)
from api_server.services.custom_phase_config.graphql.custom_phase_config_delete import (
    CustomPhaseConfigDelete,
)
from api_server.services.events.graphql.create_event import EventCreate
from api_server.services.events.graphql.delete_event import EventDelete
from api_server.services.events.graphql.event_label_option import (
    EventLabelOptionCreate,
    EventLabelOptionDelete,
    EventLabelOptionUpdate,
)
from api_server.services.events.graphql.event_type_create import EventTypeCreate
from api_server.services.events.graphql.event_type_update import EventTypeUpdate
from api_server.services.events.graphql.event_upsert import EventUpsert
from api_server.services.events.graphql.update_event import EventUpdate
from api_server.services.highlights.graphql.highlight_archive import HighlightArchive
from api_server.services.highlights.graphql.highlight_create import HighlightCreate
from api_server.services.highlights.graphql.highlight_delete import HighlightDelete
from api_server.services.highlights.graphql.highlight_feedback_create import (
    HighlightFeedbackCreate,
)
from api_server.services.highlights.graphql.highlight_feedback_update import (
    HighlightFeedbackUpdate,
)
from api_server.services.highlights.graphql.highlight_update import HighlightUpdate
from api_server.services.measurement_periods.graphql.measurement_periods_delete import (
    MeasurementPeriodDelete,
)
from api_server.services.measurement_periods.graphql.measurement_periods_upsert import (
    MeasurementPeriodUpsert,
)
from api_server.services.media.graphql.camera_capture_latest_image import (
    CameraCaptureLatestImage,
)
from api_server.services.observations.graphql.create_observation import (
    ObservationCreate,
)
from api_server.services.observations.graphql.delete_observation import (
    ObservationDelete,
)
from api_server.services.observations.graphql.observations_upsert import (
    ObservationsUpsert,
)
from api_server.services.organization.graphql.organization_create import (
    OrganizationCreate,
)
from api_server.services.organization.graphql.organization_update import (
    OrganizationUpdate,
)
from api_server.services.phases.graphql.phase_create import PhaseCreate
from api_server.services.phases.graphql.phase_delete import PhaseDelete
from api_server.services.phases.graphql.phase_relationship_create import (
    PhaseRelationshipCreate,
)
from api_server.services.phases.graphql.phase_relationship_delete import (
    PhaseRelationshipDelete,
)
from api_server.services.phases.graphql.phase_update import PhaseUpdate
from api_server.services.phases.graphql.phase_upsert import PhaseUpsert
from api_server.services.plan.graphql.case_note_plan_upsert import CaseNotePlanUpsert
from api_server.services.plan.graphql.case_staff_plan_upsert import CaseStaffPlanUpsert
from api_server.services.procedures.graphql.procedures_upsert import ProceduresUpsert
from api_server.services.room.graphql.room_create import RoomCreate
from api_server.services.room.graphql.room_tag_rename import RoomTagRename
from api_server.services.room.graphql.room_update import RoomUpdate
from api_server.services.room.graphql.room_update_configuration import RoomUpdateConfiguration
from api_server.services.room.graphql.rooms_create import RoomsCreate
from api_server.services.schedule_assistant_email.graphql.email_available_times import (
    EmailAvailableTimes,
    EmailAvailableTimesHtml,
)
from api_server.services.site.graphql.site_create import SiteCreate
from api_server.services.site.graphql.site_update import SiteUpdate
from api_server.services.staff.graphql.staff_codes_upsert import StaffCodesUpsert
from api_server.services.staff.graphql.staff_upsert import StaffUpsert
from api_server.services.staffing_needs.graphql.staffing_needs_ratio_create import (
    StaffingNeedsRatioCreate,
)
from api_server.services.turnover.graphql.turnover_goals_update import TurnoverGoalsUpdate
from api_server.services.turnover.graphql.turnover_label_note_upsert import TurnoverLabelNoteUpsert
from api_server.services.user_filter_views.graphql.user_filter_view_mutations import (
    UserFilterViewCreate,
    UserFilterViewDelete,
    UserFilterViewUpdate,
)


class Mutation(graphene.ObjectType):
    """
    This is the root of the graphql mutation schema
    All of our mutations must be added here.
    """

    event_create = EventCreate.Field()
    event_update = EventUpdate.Field()
    event_delete = EventDelete.Field()
    event_upsert = EventUpsert.Field()
    event_type_create = EventTypeCreate.Field()
    event_type_update = EventTypeUpdate.Field()

    event_label_option_create = EventLabelOptionCreate.Field()
    event_label_option_update = EventLabelOptionUpdate.Field()
    event_label_option_delete = EventLabelOptionDelete.Field()

    highlight_create = HighlightCreate.Field()
    highlight_update = HighlightUpdate.Field()
    highlight_delete = HighlightDelete.Field()
    highlight_archive = HighlightArchive.Field()

    highlight_feedback_create = HighlightFeedbackCreate.Field()
    highlight_feedback_update = HighlightFeedbackUpdate.Field()

    annotation_task_bulk_generate = AnnotationTaskBulkGenerate.Field()
    annotation_task_bulk_update = AnnotationTaskBulkUpdate.Field()
    annotation_task_update = AnnotationTaskUpdate.Field()
    annotation_task_exit = AnnotationTaskExit.Field()
    annotation_task_type_create = AnnnotationTaskTypeCreate.Field()
    annotation_task_type_update = AnnnotationTaskTypeUpdate.Field()
    annotation_task_schedule_create = AnnotationTaskScheduleCreate.Field()
    annotation_task_schedule_update = AnnotationTaskScheduleUpdate.Field()

    annotation_task_next_annotate = AnnotationTaskNextAnnotate.Field()
    annotation_task_next_review = AnnotationTaskNextReview.Field()

    camera_capture_latest_image = CameraCaptureLatestImage.Field()

    case_procedures_upsert = CaseProceduresUpsert.Field()
    process_case_derived_properties = ProcessCaseDerivedProperties.Field()
    case_note_plan_upsert = CaseNotePlanUpsert.Field()
    case_procedures_upsert_and_archive = CaseProceduresUpsertAndArchive.Field()
    case_staff_upsert = CaseStaffUpsert.Field()
    case_staff_upsert_and_archive = CaseStaffUpsertAndArchive.Field()
    case_staff_plan_upsert = CaseStaffPlanUpsert.Field()
    case_flag_upsert = CaseFlagUpsert.Field(required=True)
    match_cases = MatchCases.Field()

    procedures_upsert = ProceduresUpsert.Field()
    anesthesias_upsert = AnesthesiasUpsert.Field()

    measurement_period_delete = MeasurementPeriodDelete.Field()
    measurement_period_upsert = MeasurementPeriodUpsert.Field()

    staff_upsert = StaffUpsert.Field()
    staff_codes_upsert = StaffCodesUpsert.Field()

    phase_delete = PhaseDelete.Field()
    phase_update = PhaseUpdate.Field()
    phase_create = PhaseCreate.Field()
    phase_upsert = PhaseUpsert.Field()

    phase_relationship_delete = PhaseRelationshipDelete.Field()
    phase_relationship_create = PhaseRelationshipCreate.Field()

    observation_create = ObservationCreate.Field()
    observation_delete = ObservationDelete.Field()
    observations_upsert = ObservationsUpsert.Field()

    subscribers_upsert = SubscribersUpsert.Field()

    staffing_needs_ratio_create = StaffingNeedsRatioCreate.Field()

    notify_staff_for_events = NotifyStaffForEvents.Field()
    check_notifications_errors = CheckNotificationsErrors.Field()

    email_available_times = EmailAvailableTimes.Field()
    email_available_times_html = EmailAvailableTimesHtml.Field()

    block_create = BlockCreate.Field()
    block_update = BlockUpdate.Field()
    block_archive = BlockArchive.Field()
    block_unarchive = BlockUnarchive.Field()
    block_time_bulk_create = BlockTimeBulkCreate.Field()
    block_releases_reprocess = BlockReleaseReprocess.Field()
    block_release_process_date_range = BlockReleaseProcessDateRange.Field()
    block_release_file_transform = BlockReleaseFileTransform.Field()
    block_time_bulk_delete_duplicate = BlockTimeBulkDeleteDuplicate.Field()
    board_config_create = BoardConfigCreate.Field()
    board_config_update = BoardConfigUpdate.Field()
    board_config_delete = BoardConfigDelete.Field()

    organization_create = OrganizationCreate.Field()
    organization_update = OrganizationUpdate.Field()

    site_create = SiteCreate.Field()
    site_update = SiteUpdate.Field()
    site_prime_time_config_upsert = SitePrimeTimeConfigUpsert.Field()
    site_closure_create = SiteClosureCreate.Field()
    site_closure_delete = SiteClosureDelete.Field()
    default_site_closures_create = DefaultSiteClosuresCreate.Field()
    site_first_case_config_upsert = SiteFirstCaseConfigUpsert.Field()
    site_launches_upsert = SiteLaunchesUpsert.Field()

    room_create = RoomCreate.Field()
    rooms_create = RoomsCreate.Field()
    room_update = RoomUpdate.Field()
    room_update_configuration = RoomUpdateConfiguration.Field()

    room_tag_create = RoomTagCreate.Field()
    room_set_tags = RoomSetTags.Field()
    room_tag_rename = RoomTagRename.Field()
    room_tag_update = RoomTagUpdate.Field()

    room_closure_create = RoomClosureCreate.Field()
    room_closure_delete = RoomClosureDelete.Field()
    room_prime_time_config_upsert = RoomPrimeTimeConfigUpsert.Field()
    room_prime_time_config_delete = RoomPrimeTimeConfigDelete.Field()
    room_first_case_config_upsert = RoomFirstCaseConfigUpsert.Field()
    room_first_case_config_delete = RoomFirstCaseConfigDelete.Field()

    camera_create = CameraCreate.Field()
    cameras_create = CamerasCreate.Field()
    camera_update = CameraUpdate.Field()

    user_filter_view_create = UserFilterViewCreate.Field()
    user_filter_view_update = UserFilterViewUpdate.Field()
    user_filter_view_delete = UserFilterViewDelete.Field()

    cluster_create = ClusterCreate.Field()
    cluster_update = ClusterUpdate.Field()

    turnover_goals_update = TurnoverGoalsUpdate.Field()

    case_forecast_upsert = CaseForecastUpsert.Field()
    upsert_forecasts_for_cases = UpsertForecastsForCases.Field()

    event_dashboard_visibility_upsert = EventDashboardVisibilityUpsert.Field()
    event_dashboard_visibility_delete = EventDashboardVisibilityDelete.Field()

    terminal_clean_score_upsert = TerminalCleanScoreUpsert.Field()

    turnover_label_note_upsert = TurnoverLabelNoteUpsert.Field()

    case_to_block_overrides_upsert = CaseToBlockOverridesUpsert.Field()

    custom_phase_config_upsert = CustomPhaseConfigUpsert.Field()
    custom_phase_config_delete = CustomPhaseConfigDelete.Field()

    case_label_upsert = CaseLabelUpsert.Field()
